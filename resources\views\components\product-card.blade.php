@props(['product'])

<div class="product-card">
    <!-- Product Image -->
    <div class="product-image-wrapper relative">
        <i class="fas fa-star brand-icon absolute top-4 left-4 text-xl text-gray-800"></i>
        <img src="{{ $product->image_url }}" alt="{{ $product->name }}" class="w-full h-auto rounded-lg">
    </div>

    <!-- Card Body -->
    <div class="card-body flex-grow flex flex-col">
        <!-- Tags -->
        <div class="card-tags flex justify-between items-center mb-3 min-h-[28px]">
            @if($product->is_best_seller)
                <div class="best-seller-tag-v2 bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-semibold">
                    Best Seller
                </div>
            @else
                <span></span>
            @endif
        </div>

        <!-- Product Name -->
        <h3 class="font-bold text-lg mt-1 mb-4">{{ $product->name }}</h3>

        <!-- Card Footer -->
        <div class="card-footer flex justify-between items-center mt-auto">
            <div>
                <p class="text-sm text-gray-500">From</p>
                <p class="font-bold text-gray-800 text-lg">₵{{ number_format($product->starting_price, 2) }}</p>
            </div>
            <a href="{{ route('product.show', $product) }}" 
               class="bg-gray-900 text-white font-bold py-3 px-6 rounded-xl hover:bg-gray-800 transition">
                Print
            </a>
        </div>
    </div>
</div>
