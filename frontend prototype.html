<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PrintOnline Ghana - On-Demand Printing & Delivery</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- GSAP -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>

    <!-- Uppy JS -->
    <link href="https://releases.transloadit.com/uppy/v3.2.1/uppy.min.css" rel="stylesheet">
    <script src="https://releases.transloadit.com/uppy/v3.2.1/uppy.min.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Lato:wght@400;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Lato', sans-serif;
            background-color: #f8f9fa;
        }
        .font-heading {
            font-family: 'Poppins', sans-serif;
        }
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Poppins', sans-serif;
        }
        .top-bar {
            border-bottom: 1px solid #e5e7eb;
        }
        .hero-product-card {
            background-color: #f8f9fa;
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.1), 0 8px 10px -6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .hero-product-card:hover {
            transform: translateY(-10px);
        }
        .how-it-works-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        .step-number {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.25rem;
            color: #fff;
            background-color: #0d6efd;
            margin-bottom: 1rem;
            border: 3px solid #fff;
        }
        .product-card {
            background-color: #ffffff;
            border-radius: 1.5rem;
            padding: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .product-image-wrapper {
            border-radius: 1rem;
            position: relative;
            overflow: hidden;
        }
        .brand-icon {
            position: absolute;
            top: 1rem;
            left: 1rem;
            font-size: 1.5rem;
            color: #111827;
        }
        .card-body {
            padding: 1rem 0.5rem 0.5rem;
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }
        .card-tags {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
            min-height: 28px; /* Reserve space for tags */
        }
        .best-seller-tag-v2 {
            background-color: #d1fae5;
            color: #065f46;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: auto;
        }
        .page.hidden {
            display: none;
        }

        /* Product Info Page Styles */
        .option-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        .option-btn.selected {
            background-color: #111827;
            color: #fff;
            border-color: #111827;
        }
        .uppy-DragDrop-container {
            border: 2px dashed #d1d5db;
            background-color: #f9fafb;
            padding: 1rem;
        }
        .quantity-input {
            width: 80px;
            text-align: center;
        }
        #upload-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
        }
        .preview-image {
            width: 100px;
            height: 100px;
            border-radius: 0.5rem;
            object-fit: cover;
            border: 1px solid #e5e7eb;
        }
        
        /* Designer Card Styles */
        .designer-card {
            background-color: #fff;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -2px rgba(0,0,0,0.1);
            text-align: center;
            padding: 2rem 1.5rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .designer-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -4px rgba(0,0,0,0.1);
        }
        .designer-img {
            width: 128px;
            height: 128px;
            border-radius: 50%;
            margin: 0 auto 1.5rem;
            object-fit: cover;
            border: 4px solid #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .footer-logo {
            filter: grayscale(100%) brightness(3);
        }

        /* Reviews Slider Styles */
        @keyframes scroll {
            0% { transform: translateX(0); }
            100% { transform: translateX(-50%); }
        }
        .reviews-slider {
            overflow: hidden;
            padding: 2rem 0;
            white-space: nowrap;
            position: relative;
        }
        .reviews-slider:before,
        .reviews-slider:after {
            content: "";
            position: absolute;
            top: 0;
            width: 200px;
            height: 100%;
            z-index: 2;
        }
        .reviews-slider:before {
            left: 0;
            background: linear-gradient(to left, rgba(248, 249, 250, 0), #f8f9fa);
        }
        .reviews-slider:after {
            right: 0;
            background: linear-gradient(to right, rgba(248, 249, 250, 0), #f8f9fa);
        }
        .reviews-track {
            display: inline-block;
            animation: scroll 60s linear infinite;
        }
        .reviews-slider:hover .reviews-track {
            animation-play-state: paused;
        }
        .review-card {
            display: inline-block;
            width: 350px;
            background: #fff;
            border-radius: 1rem;
            padding: 1.5rem;
            margin: 0 1rem;
            white-space: normal;
            box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -2px rgba(0,0,0,0.1);
        }

        /* Auth Modal Styles */
        .auth-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .auth-modal {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            width: 90%;
            max-width: 400px;
            position: relative;
        }
    </style>
</head>
<body class="text-gray-800">

    <!-- Main Container -->
    <div id="app-container" class="max-w-screen-xl mx-auto">

        <!-- Home / Landing Page -->
        <div id="home-page" class="page">
            <div class="bg-white">
                <div class="top-bar"><div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-2 text-xs text-gray-600"><span>Every time. Any reason. Or we'll make it right.</span><span>Call for support: <strong>+233 24 123 4567</strong></span></div></div>
                <header class="sticky top-0 z-50 shadow-sm"><nav class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-4"><div class="flex items-center"><img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" alt="PrintOnline Ghana Logo" class="h-10 mr-3"></div><div class="hidden md:flex items-center space-x-8"><a href="#" class="text-gray-600 hover:text-pink-500 font-semibold back-to-home-link">HOME</a><a href="#" class="text-gray-600 hover:text-pink-500 font-semibold shop-nav-link">SHOP</a><a href="#" class="text-gray-600 hover:text-pink-500 font-semibold designers-nav-link">EXPERT DESIGNERS</a><a href="#how-it-works-section" class="text-gray-600 hover:text-pink-500 font-semibold">HOW IT WORKS</a></div><div class="flex items-center gap-6"><div id="account-btn" class="flex items-center gap-2 bg-gray-100 py-2 px-4 rounded-full cursor-pointer"><i class="fas fa-user-circle text-gray-600 text-xl"></i><span id="username-display" class="text-sm font-semibold text-gray-700">Account</span></div></div></nav></header>
            </div>
            <main class="bg-white">
                <section class="relative bg-white pt-16 pb-24 sm:pt-24 sm:pb-32"><div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 grid md:grid-cols-2 gap-12 items-center"><div class="text-center md:text-left"><h1 class="font-heading text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight text-gray-900">PRINT WHATEVER YOU WANT</h1><p class="mt-6 text-lg text-gray-600">From t-shirt design to stationery we print everything. We do everything and everything!</p><button id="order-now-btn" class="font-heading mt-8 bg-pink-500 text-white font-bold py-3 px-8 rounded-lg text-lg hover:bg-pink-600 transition duration-300 transform hover:scale-105">Send For Printing <i class="fas fa-arrow-right ml-2"></i></button></div><div class="relative h-96"><div class="hero-product-card absolute top-0 left-10 w-48" style="transform: rotate(-10deg);"><img src="https://printy.themerex.net/wp-content/uploads/2023/11/img-12-copyright.png" alt="T-Shirt" class="w-full h-auto"><p class="text-center font-semibold text-sm p-2 bg-white">T-SHIRTS AND APPAREL</p></div><div class="hero-product-card absolute top-1/4 left-1/3 w-40 z-10" style="transform: rotate(5deg);"><img src="https://printy.themerex.net/wp-content/uploads/2023/11/product-11-copyright-630x630.jpg" alt="Bottle" class="w-full h-auto"><p class="text-center font-semibold text-sm p-2 bg-white">PRODUCT AND LABEL</p></div><div class="hero-product-card absolute top-10 right-10 w-40" style="transform: rotate(15deg);"><img src="https://printy.themerex.net/wp-content/uploads/2023/11/product-12-copyright-630x630.jpg" alt="Phone Case" class="w-full h-auto"><p class="text-center font-semibold text-sm p-2 bg-white">PHONE BACK COVERS</p></div></div></div></section>
                <section id="products-section" class="py-24 bg-gray-50">
                    <div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
                        <h2 class="text-3xl font-bold text-center mb-16 text-gray-900">Featured Products</h2>
                        <div id="product-card-container" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                            <!-- Product cards will be dynamically inserted here -->
                        </div>
                    </div>
                </section>
                <section id="how-it-works-section" class="relative py-24" style="background-color: #0d6efd;"><div class="relative max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 z-10"><h2 class="text-3xl font-bold text-center mb-16 text-white">HOW IT WORKS</h2><div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8 text-white"><div class="how-it-works-step"><div class="step-number">01</div><h3 class="font-semibold mb-2">Choose Product</h3><p class="text-sm opacity-80">Choose the product you want from our catalog.</p></div><div class="how-it-works-step"><div class="step-number">02</div><h3 class="font-semibold mb-2">Upload Design</h3><p class="text-sm opacity-80">Apply your pre-uploaded designs or create a new one.</p></div><div class="how-it-works-step"><div class="step-number">03</div><h3 class="font-semibold mb-2">Place Order</h3><p class="text-sm opacity-80">Complete your design, make payment and place order.</p></div><div class="how-it-works-step"><div class="step-number">04</div><h3 class="font-semibold mb-2">Get Delivery</h3><p class="text-sm opacity-80">Get your product delivered at your doorstep.</p></div></div></div></section>
                
                <!-- Google Reviews Section -->
                <section id="reviews-section" class="py-24 bg-gray-50">
                    <div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
                        <h2 class="text-3xl font-bold text-center mb-4 text-gray-900">What Our Customers Say</h2>
                        <p class="text-center text-gray-600 mb-8">Rated 4.9/5.0 on Google Reviews</p>
                        <div class="reviews-slider">
                            <div id="reviews-track" class="reviews-track">
                                <!-- Review cards will be dynamically inserted here -->
                            </div>
                        </div>
                    </div>
                </section>

                <section id="cta-section" class="py-20 bg-white"><div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center"><h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">Have a design? Let's bring it to life.</h2><p class="mt-4 text-lg text-gray-600">Our high-quality printing services are ready for your creativity. Upload your design and get started today.</p><a href="#" class="mt-8 inline-block bg-pink-500 text-white font-bold py-4 px-10 rounded-lg text-lg hover:bg-pink-600 transition duration-300 font-heading">Upload & Print</a></div></section>
            </main>
            <footer class="bg-gray-900 text-white"><div class="max-w-screen-xl mx-auto py-12 px-4 sm:px-6 lg:px-8"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"><div class="lg:col-span-1"><div class="flex items-center"><img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" alt="PrintOnline Ghana Logo" class="h-10 footer-logo"></div><p class="mt-4 text-gray-400 text-sm">The best place to get your custom designs printed on high-quality products. Fast delivery across Ghana.</p></div><div><h3 class="text-sm font-semibold tracking-wider uppercase">Quick Links</h3><ul class="mt-4 space-y-2"><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Home</a></li><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Apparel</a></li><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Office</a></li><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Gifts</a></li></ul></div><div><h3 class="text-sm font-semibold tracking-wider uppercase">Contact Us</h3><ul class="mt-4 space-y-3 text-gray-400 text-sm"><li class="flex items-start"><i class="fas fa-map-marker-alt mt-1 mr-3 flex-shrink-0"></i><span>123 Oxford Street, Osu, Accra, Ghana</span></li><li class="flex items-center"><i class="fas fa-phone-alt mr-3"></i><span>+233 24 123 4567</span></li><li class="flex items-center"><i class="fas fa-envelope mr-3"></i><span><EMAIL></span></li></ul></div><div><h3 class="text-sm font-semibold tracking-wider uppercase">Follow Us</h3><div class="flex mt-4 space-x-6"><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-facebook-f fa-lg"></i></a><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-instagram fa-lg"></i></a><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-twitter fa-lg"></i></a><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-linkedin-in fa-lg"></i></a></div></div></div><div class="mt-12 border-t border-gray-800 pt-8 text-center text-sm text-gray-400"><p>&copy; 2025 PrintOnline Ghana. All rights reserved.</p></div></div></footer>
        </div>

        <!-- Shop Page -->
        <div id="shop-page" class="page hidden bg-gray-50">
            <div class="bg-white">
                <div class="top-bar"><div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-2 text-xs text-gray-600"><span>Every time. Any reason. Or we'll make it right.</span><span>Call for support: <strong>+233 24 123 4567</strong></span></div></div>
                <header class="sticky top-0 z-50 shadow-sm"><nav class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-4"><div class="flex items-center"><img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" alt="PrintOnline Ghana Logo" class="h-10 mr-3"></div><div class="hidden md:flex items-center space-x-8"><a href="#" class="text-gray-600 hover:text-pink-500 font-semibold back-to-home-link">HOME</a><a href="#" class="text-gray-600 hover:text-pink-500 font-semibold shop-nav-link">SHOP</a><a href="#" class="text-gray-600 hover:text-pink-500 font-semibold designers-nav-link">EXPERT DESIGNERS</a><a href="#how-it-works-section" class="text-gray-600 hover:text-pink-500 font-semibold">HOW IT WORKS</a></div><div class="flex items-center gap-6"><div id="account-btn-shop" class="flex items-center gap-2 bg-gray-100 py-2 px-4 rounded-full cursor-pointer"><i class="fas fa-user-circle text-gray-600 text-xl"></i><span id="username-display-shop" class="text-sm font-semibold text-gray-700">Account</span></div></div></nav></header>
            </div>
            <main class="p-4 md:p-8 lg:p-12">
                <div class="max-w-screen-xl mx-auto">
                    <div class="text-center mb-16">
                        <h1 class="text-4xl font-bold text-gray-900">Our Products</h1>
                        <p class="mt-4 text-lg text-gray-600">Browse our collection of high-quality printable items.</p>
                    </div>
                    <div id="shop-product-card-container" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
                        <!-- Shop product cards will be dynamically inserted here -->
                    </div>
                </div>
            </main>
            <footer class="bg-gray-900 text-white"><div class="max-w-screen-xl mx-auto py-12 px-4 sm:px-6 lg:px-8"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"><div class="lg:col-span-1"><div class="flex items-center"><img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" alt="PrintOnline Ghana Logo" class="h-10 footer-logo"></div><p class="mt-4 text-gray-400 text-sm">The best place to get your custom designs printed on high-quality products. Fast delivery across Ghana.</p></div><div><h3 class="text-sm font-semibold tracking-wider uppercase">Quick Links</h3><ul class="mt-4 space-y-2"><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Home</a></li><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Apparel</a></li><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Office</a></li><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Gifts</a></li></ul></div><div><h3 class="text-sm font-semibold tracking-wider uppercase">Contact Us</h3><ul class="mt-4 space-y-3 text-gray-400 text-sm"><li class="flex items-start"><i class="fas fa-map-marker-alt mt-1 mr-3 flex-shrink-0"></i><span>123 Oxford Street, Osu, Accra, Ghana</span></li><li class="flex items-center"><i class="fas fa-phone-alt mr-3"></i><span>+233 24 123 4567</span></li><li class="flex items-center"><i class="fas fa-envelope mr-3"></i><span><EMAIL></span></li></ul></div><div><h3 class="text-sm font-semibold tracking-wider uppercase">Follow Us</h3><div class="flex mt-4 space-x-6"><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-facebook-f fa-lg"></i></a><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-instagram fa-lg"></i></a><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-twitter fa-lg"></i></a><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-linkedin-in fa-lg"></i></a></div></div></div><div class="mt-12 border-t border-gray-800 pt-8 text-center text-sm text-gray-400"><p>&copy; 2025 PrintOnline Ghana. All rights reserved.</p></div></div></footer>
        </div>

        <!-- Product Info Page -->
        <div id="product-page" class="page hidden bg-white">
             <div class="bg-white">
                <div class="top-bar"><div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-2 text-xs text-gray-600"><span>Every time. Any reason. Or we'll make it right.</span><span>Call for support: <strong>+233 24 123 4567</strong></span></div></div>
                <header class="sticky top-0 z-50 shadow-sm"><nav class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-4"><div class="flex items-center"><img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" alt="PrintOnline Ghana Logo" class="h-10 mr-3"></div><div class="hidden md:flex items-center space-x-8"><a href="#" class="text-gray-600 hover:text-pink-500 font-semibold back-to-home-link">HOME</a><a href="#" class="text-gray-600 hover:text-pink-500 font-semibold shop-nav-link">SHOP</a><a href="#" class="text-gray-600 hover:text-pink-500 font-semibold designers-nav-link">EXPERT DESIGNERS</a><a href="#how-it-works-section" class="text-gray-600 hover:text-pink-500 font-semibold">HOW IT WORKS</a></div><div class="flex items-center gap-6"><div id="account-btn-product" class="flex items-center gap-2 bg-gray-100 py-2 px-4 rounded-full cursor-pointer"><i class="fas fa-user-circle text-gray-600 text-xl"></i><span id="username-display-product" class="text-sm font-semibold text-gray-700">Account</span></div></div></nav></header>
            </div>
            <div class="p-4 md:p-8 lg:p-12">
                <div class="max-w-screen-xl mx-auto">
                    <p id="breadcrumbs" class="text-sm text-gray-500 mb-8"><a href="#" class="hover:text-gray-800 back-to-home-link">Home</a> / <span id="breadcrumb-category">Category</span> / <span id="breadcrumb-product" class="font-medium text-gray-800">Product</span></p>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                        <!-- Left Column: Product Image -->
                        <div class="bg-orange-200 rounded-2xl flex items-center justify-center p-8">
                            <img id="product-info-image" src="https://placehold.co/600x600/FFFFFF/F97316?text=Product+Image" alt="Product Image" class="max-w-full h-auto">
                        </div>

                        <!-- Right Column: Product Details -->
                        <div>
                            <h1 id="product-info-title" class="text-4xl font-bold">Product Title</h1>
                            <p class="text-gray-500 mt-2">By PrintOnline Ghana</p>
                            
                            <div class="flex items-center mt-4">
                                <p id="product-info-price" class="text-3xl font-bold text-orange-500">₵0.00</p>
                            </div>

                            <p id="product-info-description" class="mt-6 text-gray-600"></p>

                            <div id="product-info-options" class="mt-8 space-y-6">
                                <!-- Dynamic options will be inserted here -->
                            </div>

                            <div class="mt-8">
                                <h3 class="text-md font-semibold text-gray-800 mb-3">Upload Your Design</h3>
                                <div id="upload-progress-bar"></div>
                                <div id="product-info-uppy"></div>
                                <div id="upload-preview-container"></div>
                                <div id="upload-status-bar"></div>
                            </div>
                            
                            <div class="flex items-center mt-8 gap-4">
                                <button id="submit-order-btn" class="flex-grow bg-orange-500 text-white font-bold py-4 rounded-lg hover:bg-orange-600 transition font-heading">Print</button>
                            </div>
                            
                            <div class="mt-8 border-t pt-6">
                                <h3 class="font-bold text-center">Guaranteed safe checkout</h3>
                                <div class="flex justify-center items-center gap-4 mt-4 text-3xl text-gray-400">
                                    <i class="fab fa-cc-gopay"></i>
                                    <i class="fab fa-cc-paypal"></i>
                                    <i class="fab fa-cc-visa"></i>
                                    <i class="fab fa-cc-mastercard"></i>
                                    <i class="fab fa-cc-amex"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <footer class="bg-gray-900 text-white"><div class="max-w-screen-xl mx-auto py-12 px-4 sm:px-6 lg:px-8"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"><div class="lg:col-span-1"><div class="flex items-center"><img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" alt="PrintOnline Ghana Logo" class="h-10 footer-logo"></div><p class="mt-4 text-gray-400 text-sm">The best place to get your custom designs printed on high-quality products. Fast delivery across Ghana.</p></div><div><h3 class="text-sm font-semibold tracking-wider uppercase">Quick Links</h3><ul class="mt-4 space-y-2"><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Home</a></li><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Apparel</a></li><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Office</a></li><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Gifts</a></li></ul></div><div><h3 class="text-sm font-semibold tracking-wider uppercase">Contact Us</h3><ul class="mt-4 space-y-3 text-gray-400 text-sm"><li class="flex items-start"><i class="fas fa-map-marker-alt mt-1 mr-3 flex-shrink-0"></i><span>123 Oxford Street, Osu, Accra, Ghana</span></li><li class="flex items-center"><i class="fas fa-phone-alt mr-3"></i><span>+233 24 123 4567</span></li><li class="flex items-center"><i class="fas fa-envelope mr-3"></i><span><EMAIL></span></li></ul></div><div><h3 class="text-sm font-semibold tracking-wider uppercase">Follow Us</h3><div class="flex mt-4 space-x-6"><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-facebook-f fa-lg"></i></a><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-instagram fa-lg"></i></a><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-twitter fa-lg"></i></a><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-linkedin-in fa-lg"></i></a></div></div></div><div class="mt-12 border-t border-gray-800 pt-8 text-center text-sm text-gray-400"><p>&copy; 2025 PrintOnline Ghana. All rights reserved.</p></div></div></footer>
        </div>

        <!-- Expert Designers Page -->
        <div id="designers-page" class="page hidden bg-gray-50">
            <div class="bg-white">
                <div class="top-bar"><div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-2 text-xs text-gray-600"><span>Every time. Any reason. Or we'll make it right.</span><span>Call for support: <strong>+233 24 123 4567</strong></span></div></div>
                <header class="sticky top-0 z-50 shadow-sm"><nav class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-4"><div class="flex items-center"><img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" alt="PrintOnline Ghana Logo" class="h-10 mr-3"></div><div class="hidden md:flex items-center space-x-8"><a href="#" class="text-gray-600 hover:text-pink-500 font-semibold back-to-home-link">HOME</a><a href="#" class="text-gray-600 hover:text-pink-500 font-semibold shop-nav-link">SHOP</a><a href="#" class="text-gray-600 hover:text-pink-500 font-semibold designers-nav-link">EXPERT DESIGNERS</a><a href="#how-it-works-section" class="text-gray-600 hover:text-pink-500 font-semibold">HOW IT WORKS</a></div><div class="flex items-center gap-6"><div id="account-btn-designers" class="flex items-center gap-2 bg-gray-100 py-2 px-4 rounded-full cursor-pointer"><i class="fas fa-user-circle text-gray-600 text-xl"></i><span id="username-display-designers" class="text-sm font-semibold text-gray-700">Account</span></div></div></nav></header>
            </div>
            <main class="p-4 md:p-8 lg:p-12">
                <div class="max-w-screen-xl mx-auto">
                    <div class="text-center mb-16">
                        <h1 class="text-4xl font-bold text-gray-900">Meet Our Expert Designers</h1>
                        <p class="mt-4 text-lg text-gray-600">Find the perfect designer to bring your vision to life.</p>
                    </div>
                    <div id="designer-card-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                        <!-- Designer cards will be dynamically inserted here -->
                    </div>
                </div>
            </main>
            <footer class="bg-gray-900 text-white"><div class="max-w-screen-xl mx-auto py-12 px-4 sm:px-6 lg:px-8"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"><div class="lg:col-span-1"><div class="flex items-center"><img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" alt="PrintOnline Ghana Logo" class="h-10 footer-logo"></div><p class="mt-4 text-gray-400 text-sm">The best place to get your custom designs printed on high-quality products. Fast delivery across Ghana.</p></div><div><h3 class="text-sm font-semibold tracking-wider uppercase">Quick Links</h3><ul class="mt-4 space-y-2"><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Home</a></li><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Apparel</a></li><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Office</a></li><li><a href="#" class="text-base text-gray-400 hover:text-white transition">Gifts</a></li></ul></div><div><h3 class="text-sm font-semibold tracking-wider uppercase">Contact Us</h3><ul class="mt-4 space-y-3 text-gray-400 text-sm"><li class="flex items-start"><i class="fas fa-map-marker-alt mt-1 mr-3 flex-shrink-0"></i><span>123 Oxford Street, Osu, Accra, Ghana</span></li><li class="flex items-center"><i class="fas fa-phone-alt mr-3"></i><span>+233 24 123 4567</span></li><li class="flex items-center"><i class="fas fa-envelope mr-3"></i><span><EMAIL></span></li></ul></div><div><h3 class="text-sm font-semibold tracking-wider uppercase">Follow Us</h3><div class="flex mt-4 space-x-6"><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-facebook-f fa-lg"></i></a><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-instagram fa-lg"></i></a><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-twitter fa-lg"></i></a><a href="#" class="text-gray-400 hover:text-white transition"><i class="fab fa-linkedin-in fa-lg"></i></a></div></div></div><div class="mt-12 border-t border-gray-800 pt-8 text-center text-sm text-gray-400"><p>&copy; 2025 PrintOnline Ghana. All rights reserved.</p></div></div></footer>
        </div>

        <!-- Auth Modal Page -->
        <div id="auth-page" class="page hidden auth-modal-overlay">
            <div class="auth-modal">
                <button id="close-auth-modal" class="absolute top-4 right-4 text-gray-500 hover:text-gray-800">&times;</button>
                <div id="login-form-container">
                    <h2 class="text-2xl font-bold text-center mb-6">Login</h2>
                    <form id="login-form" class="space-y-4">
                        <input type="email" placeholder="Email Address" class="w-full p-3 border rounded-lg" required>
                        <input type="password" placeholder="Password" class="w-full p-3 border rounded-lg" required>
                        <button type="submit" class="w-full bg-pink-500 text-white font-bold py-3 rounded-lg hover:bg-pink-600 transition">Login</button>
                        <p class="text-center text-sm">Don't have an account? <a href="#" id="show-signup" class="font-semibold text-pink-500">Sign Up</a></p>
                    </form>
                </div>
                <div id="signup-form-container" class="hidden">
                    <h2 class="text-2xl font-bold text-center mb-6">Create Account</h2>
                    <form id="signup-form" class="space-y-4">
                        <input type="text" id="signup-name" placeholder="Full Name" class="w-full p-3 border rounded-lg" required>
                        <input type="email" placeholder="Email Address" class="w-full p-3 border rounded-lg" required>
                        <input type="password" placeholder="Password" class="w-full p-3 border rounded-lg" required>
                        <button type="submit" class="w-full bg-pink-500 text-white font-bold py-3 rounded-lg hover:bg-pink-600 transition">Create Account</button>
                        <p class="text-center text-sm">Already have an account? <a href="#" id="show-login" class="font-semibold text-pink-500">Login</a></p>
                    </form>
                </div>
            </div>
        </div>

        <!-- Order Tracking Dashboard (Hidden) -->
        <div id="dashboard-page" class="page hidden"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- DATA ---
            const productData = {
                "Custom T-Shirt": { name: "Custom T-Shirt", category: "Apparel", isBestSeller: true, image: "https://placehold.co/400x400/F87171/FFFFFF?text=Shirt", description: "Our best-selling custom t-shirt is made from 100% premium cotton...", options: [ { name: "Size", type: "button", values: [ { name: 'S', price: 30.00 }, { name: 'M', price: 30.00 }, { name: 'L', price: 35.00 }, { name: 'XL', price: 38.00 } ] }, { name: "Color", type: "color", values: [ { name: 'White', value: '#FFFFFF', price: 0 }, { name: 'Black', value: '#000000', price: 0 }, { name: 'Blue', value: '#3B82F6', price: 0 } ] } ] },
                "Personalized Cup": { name: "Personalized Cup", category: "Giftware", isBestSeller: false, image: "https://placehold.co/400x400/60A5FA/FFFFFF?text=Cup", description: "Start your day with a personal touch...", options: [ { name: "Material", type: "button", values: [ { name: 'Ceramic', price: 20.00 }, { name: 'Plastic', price: 15.00 }, { name: 'Bamboo', price: 25.00 } ] } ] },
                "Business Cards": { name: "Business Cards", category: "Office", isBestSeller: true, image: "https://placehold.co/400x400/34D399/FFFFFF?text=Cards", description: "Make a lasting impression...", options: [ { name: "Type", type: "button", values: [ { name: 'Matte', price: 150.00 }, { name: 'Glossy', price: 160.00 }, { name: 'Untearable', price: 200.00 } ] }, { name: "Pack Size", type: "quantity", values: [ { name: 'x100', multiplier: 1 }, { name: 'x200', multiplier: 1.8 }, { name: 'x500', multiplier: 4 } ] } ] },
                "A5 Flyers": { name: "A5 Flyers", category: "Marketing", isBestSeller: false, image: "https://placehold.co/400x400/FBBF24/FFFFFF?text=Flyer", description: "Promote your event or business...", options: [ { name: "Paper Weight", type: "button", values: [ { name: '150gsm', price: 200.00 }, { name: '250gsm', price: 250.00 }, { name: '350gsm', price: 300.00 } ] }, { name: "Pack Size", type: "quantity", values: [ { name: 'x100', multiplier: 1 }, { name: 'x250', multiplier: 2.2 }, { name: 'x500', multiplier: 4 } ] } ] },
                "Custom Hoodie": { name: "Custom Hoodie", category: "Apparel", isBestSeller: false, image: "https://placehold.co/400x400/9CA3AF/FFFFFF?text=Hoodie", description: "Stay warm and stylish...", options: [ { name: "Size", type: "button", values: [ { name: 'M', price: 80.00 }, { name: 'L', price: 85.00 }, { name: 'XL', price: 90.00 } ] }, { name: "Color", type: "color", values: [ { name: 'Grey', value: '#6B7280', price: 0 }, { name: 'Black', value: '#1F2937', price: 0 } ] } ] },
                "Tote Bag": { name: "Tote Bag", category: "Accessories", isBestSeller: true, image: "https://placehold.co/400x400/FCD34D/FFFFFF?text=Tote", description: "A durable and eco-friendly tote bag...", options: [ { name: "Material", type: "button", values: [ { name: 'Canvas', price: 25.00 }, { name: 'Cotton', price: 22.00 } ] } ] },
                "A3 Poster": { name: "A3 Poster", category: "Marketing", isBestSeller: false, image: "https://placehold.co/400x400/A78BFA/FFFFFF?text=Poster", description: "High-quality A3 posters...", options: [ { name: "Finish", type: "button", values: [ { name: 'Matte', price: 50.00 }, { name: 'Gloss', price: 55.00 } ] } ] },
                "Roll-up Banner": { name: "Roll-up Banner", category: "Marketing", isBestSeller: false, image: "https://placehold.co/400x400/38BDF8/FFFFFF?text=Banner", description: "Make a big impact...", options: [ { name: "Size", type: "button", values: [ { name: 'Standard', price: 450.00 }, { name: 'Large', price: 600.00 } ] } ] },
                "Sticker Sheet": { name: "Sticker Sheet (A4)", category: "Office", isBestSeller: true, image: "https://placehold.co/400x400/FB923C/FFFFFF?text=Stickers", description: "Create custom die-cut stickers...", options: [ { name: "Finish", type: "button", values: [ { name: 'Matte', price: 40.00 }, { name: 'Glossy', price: 45.00 } ] } ] },
                "Custom Jumper": { name: "Custom Jumper", category: "Apparel", isBestSeller: false, image: "https://placehold.co/400x400/4B5563/FFFFFF?text=Jumper", description: "A comfortable and warm jumper...", options: [ { name: "Size", type: "button", values: [ { name: 'M', price: 70.00 }, { name: 'L', price: 75.00 }, { name: 'XL', price: 80.00 } ] }, { name: "Color", type: "color", values: [ { name: 'Navy', value: '#1E3A8A', price: 0 }, { name: 'Charcoal', value: '#374151', price: 0 } ] } ] }
            };
            
            const designerData = [
                { name: "Ama Serwaa", image: "https://placehold.co/128x128/E9D5FF/4C1D95?text=AS", bio: "Specializing in vibrant branding and minimalist logo design.", portfolioUrl: "#", socials: { behance: "#", instagram: "#" } },
                { name: "Kofi Mensah", image: "https://placehold.co/128x128/BBF7D0/14532D?text=KM", bio: "Expert in corporate identity and print layout design.", portfolioUrl: "#", socials: { linkedin: "#", dribbble: "#" } },
                { name: "Yaa Asantewaa", image: "https://placehold.co/128x128/FECACA/7F1D1D?text=YA", bio: "Creative illustrator and t-shirt design specialist.", portfolioUrl: "#", socials: { instagram: "#", twitter: "#" } }
            ];

            const reviewsData = [
                { name: "Kwame Addo", avatar: "KA", text: "Incredible quality and fast delivery! My business cards look amazing. Will definitely be ordering again." },
                { name: "Adwoa Boateng", avatar: "AB", text: "The t-shirt printing was flawless. The colors are so vibrant and the material is top-notch. Highly recommended!" },
                { name: "Femi Adebayo", avatar: "FA", text: "I'm so impressed with the customer service. They helped me with my design and the final product was perfect." },
                { name: "Esi Mensah", avatar: "EM", text: "My custom mugs were a huge hit at the office. Great quality and they arrived right on time for our event." },
                { name: "Chidi Okoro", avatar: "CO", text: "Fast, reliable, and professional. PrintOnline Ghana is my go-to for all my printing needs." },
                { name: "Nana Akua", avatar: "NA", text: "The flyers they printed for my business were stunning. The print quality is excellent and the paper feels premium." },
                { name: "Bayo Olumide", avatar: "BO", text: "Their website is so easy to use. Uploading my design and placing an order was a breeze. Great experience!" },
                { name: "Zainab Ibrahim", avatar: "ZI", text: "I ordered custom tote bags and they exceeded my expectations. Fantastic quality and beautiful printing." }
            ];
            
            let currentUser = null;

            // --- ELEMENTS ---
            const pages = { home: document.getElementById('home-page'), shop: document.getElementById('shop-page'), product: document.getElementById('product-page'), designers: document.getElementById('designers-page'), auth: document.getElementById('auth-page') };
            const productCardContainer = document.getElementById('product-card-container');
            const shopProductCardContainer = document.getElementById('shop-product-card-container');
            const designerCardContainer = document.getElementById('designer-card-container');
            const reviewsTrack = document.getElementById('reviews-track');
            const loginFormContainer = document.getElementById('login-form-container');
            const signupFormContainer = document.getElementById('signup-form-container');

            // --- PAGE NAVIGATION & SETUP ---
            function showPage(pageName) {
                Object.values(pages).forEach(page => {
                    if (page.id !== 'auth-page') page.classList.add('hidden');
                });
                if (pages[pageName]) {
                    pages[pageName].classList.remove('hidden');
                }
                window.scrollTo(0, 0);
            }
            
            function setupAndShowProductPage(productName) {
                setupProductInfoPage(productName);
                showPage('product');
            }

            // --- DYNAMIC CONTENT GENERATION ---
            function generateProductCards(container, productList) {
                container.innerHTML = '';
                for (const productName in productList) {
                    const product = productList[productName];
                    const startingPrice = Math.min(...product.options[0].values.map(v => v.price));
                    const cardHTML = `<div class="product-card"><div class="product-image-wrapper"><i class="fas fa-star brand-icon"></i><img src="${product.image}" alt="${product.name}" class="w-full h-auto"></div><div class="card-body"><div class="card-tags">${product.isBestSeller ? '<div class="best-seller-tag-v2">Best Seller</div>' : '<span></span>'}</div><h3 class="font-bold text-lg mt-1 mb-4">${product.name}</h3><div class="card-footer"><div><p class="text-sm text-gray-500">From</p><p class="font-bold text-gray-800 text-lg">₵${startingPrice.toFixed(2)}</p></div><button class="print-btn bg-gray-900 text-white font-bold py-3 px-6 rounded-xl hover:bg-gray-800 transition" data-product="${product.name}">Print</button></div></div></div>`;
                    container.insertAdjacentHTML('beforeend', cardHTML);
                }
            }

            function generateDesignerCards() {
                designerCardContainer.innerHTML = '';
                designerData.forEach(designer => {
                    const socialLinks = Object.entries(designer.socials).map(([platform, url]) => `<a href="${url}" target="_blank" class="text-gray-400 hover:text-pink-500 transition"><i class="fab fa-${platform} fa-lg"></i></a>`).join('');
                    const cardHTML = `<div class="designer-card"><img src="${designer.image}" alt="${designer.name}" class="designer-img"><h3 class="text-xl font-bold text-gray-900">${designer.name}</h3><p class="text-gray-500 my-4">${designer.bio}</p><div class="flex justify-center items-center gap-x-5 mt-4">${socialLinks}<a href="${designer.portfolioUrl}" target="_blank" class="text-sm font-semibold text-pink-500 hover:text-pink-600">Portfolio <i class="fas fa-arrow-right ml-1"></i></a></div></div>`;
                    designerCardContainer.insertAdjacentHTML('beforeend', cardHTML);
                });
            }

            function generateReviewsSlider() {
                const reviews = [...reviewsData, ...reviewsData]; // Duplicate for seamless loop
                reviewsTrack.innerHTML = '';
                reviews.forEach(review => {
                    const cardHTML = `
                        <div class="review-card">
                            <div class="flex items-start justify-between mb-4">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/c/c1/Google_%22G%22_logo.svg" alt="Google" class="h-6">
                                <div class="text-yellow-400">
                                    <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 text-sm leading-relaxed">"${review.text}"</p>
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center font-bold text-gray-600 mr-3">
                                    ${review.avatar}
                                </div>
                                <span class="font-semibold text-gray-800">${review.name}</span>
                            </div>
                        </div>`;
                    reviewsTrack.insertAdjacentHTML('beforeend', cardHTML);
                });
            }

            let uppy;
            function setupProductInfoPage(productName) {
                const product = productData[productName];
                if (!product) return;

                document.getElementById('breadcrumb-category').textContent = product.category;
                document.getElementById('breadcrumb-product').textContent = product.name;
                document.getElementById('product-info-title').textContent = product.name;
                document.getElementById('product-info-image').src = product.image.replace('400x400', '600x600');
                document.getElementById('product-info-description').textContent = product.description;

                const optionsContainer = document.getElementById('product-info-options');
                optionsContainer.innerHTML = '';
                product.options.forEach((option, optionIndex) => {
                    const optionDiv = document.createElement('div');
                    const label = document.createElement('h3');
                    label.className = 'text-md font-semibold text-gray-800 mb-3';
                    label.textContent = option.name;
                    optionDiv.appendChild(label);
                    const valuesDiv = document.createElement('div');
                    valuesDiv.className = 'flex items-center gap-3 flex-wrap';
                    
                    option.values.forEach((value, valueIndex) => {
                        const btn = document.createElement('button');
                        btn.className = 'option-btn';
                        btn.dataset.optionIndex = optionIndex;
                        btn.dataset.valueIndex = valueIndex;
                        if (option.type === 'color') {
                            btn.style.backgroundColor = value.value;
                            btn.style.width = '24px';
                            btn.style.height = '24px';
                            btn.style.borderRadius = '50%';
                        } else {
                            btn.textContent = value.name;
                        }
                        if (valueIndex === 0) btn.classList.add('selected');
                        valuesDiv.appendChild(btn);
                    });
                    optionDiv.appendChild(valuesDiv);
                    optionsContainer.appendChild(optionDiv);
                });

                updatePrice();

                if (uppy) uppy.close();
                const previewContainer = document.getElementById('upload-preview-container');
                previewContainer.innerHTML = '';
                uppy = new Uppy.Uppy({ autoProceed: false, restrictions: { maxNumberOfFiles: 1, maxFileSize: 50 * 1024 * 1024 } })
                .use(Uppy.DragDrop, { target: '#product-info-uppy' })
                .use(Uppy.ProgressBar, { target: '#upload-progress-bar', hideAfterFinish: false })
                .use(Uppy.StatusBar, { target: '#upload-status-bar', hideAfterFinish: true })
                .use(Uppy.ThumbnailGenerator, { thumbnailWidth: 200 });
                uppy.on('thumbnail:generated', (file, preview) => {
                    const img = document.createElement('img');
                    img.src = preview;
                    img.alt = file.name;
                    img.className = 'preview-image';
                    previewContainer.innerHTML = '';
                    previewContainer.appendChild(img);
                });
            }

            function updatePrice() {
                const title = document.getElementById('product-info-title').textContent;
                const product = productData[title];
                if (!product) return;

                let currentPrice = 0;
                let multiplier = 1;

                document.querySelectorAll('#product-info-options .option-btn.selected').forEach(btn => {
                    const optionIndex = parseInt(btn.dataset.optionIndex);
                    const valueIndex = parseInt(btn.dataset.valueIndex);
                    const option = product.options[optionIndex];
                    const value = option.values[valueIndex];
                    
                    if (option.type === 'quantity') {
                        multiplier = value.multiplier;
                    } else {
                        currentPrice += value.price;
                    }
                });

                const finalPrice = currentPrice * multiplier;
                document.getElementById('product-info-price').textContent = `₵${finalPrice.toFixed(2)}`;
            }
            
            function updateAccountPills() {
                const username = currentUser ? currentUser.name.split(' ')[0] : 'Account';
                document.querySelectorAll('[id^="username-display"]').forEach(el => el.textContent = username);
            }

            // --- EVENT LISTENERS ---
            document.body.addEventListener('click', (e) => {
                if (e.target.closest('.print-btn')) {
                    const productName = e.target.closest('.print-btn').dataset.product;
                    setupAndShowProductPage(productName);
                }
                if (e.target.closest('.back-to-home-link')) {
                    e.preventDefault();
                    showPage('home');
                }
                if (e.target.closest('.designers-nav-link')) {
                    e.preventDefault();
                    showPage('designers');
                }
                if (e.target.closest('.shop-nav-link')) {
                    e.preventDefault();
                    showPage('shop');
                }
                if (e.target.closest('.option-btn')) {
                    const btn = e.target.closest('.option-btn');
                    const parent = btn.parentElement;
                    parent.querySelectorAll('.option-btn').forEach(b => b.classList.remove('selected'));
                    btn.classList.add('selected');
                    updatePrice();
                }
                if (e.target.closest('#submit-order-btn')) {
                    if (!currentUser) {
                        alert('Please log in or create an account to place an order.');
                        showPage('auth');
                    } else {
                        alert('Order submitted successfully! (Simulation)');
                    }
                }
                if (e.target.closest('[id^="account-btn"]')) {
                    if (currentUser) {
                        if (confirm('Are you sure you want to log out?')) {
                            currentUser = null;
                            updateAccountPills();
                        }
                    } else {
                        showPage('auth');
                    }
                }
                if (e.target.closest('#close-auth-modal') || e.target.id === 'auth-page') {
                    pages.auth.classList.add('hidden');
                }
                if (e.target.closest('#show-signup')) {
                    e.preventDefault();
                    loginFormContainer.classList.add('hidden');
                    signupFormContainer.classList.remove('hidden');
                }
                if (e.target.closest('#show-login')) {
                    e.preventDefault();
                    signupFormContainer.classList.add('hidden');
                    loginFormContainer.classList.remove('hidden');
                }
            });
            
            document.getElementById('login-form').addEventListener('submit', (e) => {
                e.preventDefault();
                currentUser = { name: "Kofi Mensah" }; // Simulate login
                updateAccountPills();
                pages.auth.classList.add('hidden');
                showPage('home');
            });
            
            document.getElementById('signup-form').addEventListener('submit', (e) => {
                e.preventDefault();
                const name = document.getElementById('signup-name').value;
                currentUser = { name: name }; // Simulate signup and login
                updateAccountPills();
                pages.auth.classList.add('hidden');
                showPage('home');
            });


            // --- INITIALIZATION ---
            const featuredProducts = Object.fromEntries(Object.entries(productData).slice(0, 4));
            generateProductCards(productCardContainer, featuredProducts);
            generateProductCards(shopProductCardContainer, productData);
            generateDesignerCards();
            generateReviewsSlider();
            gsap.from(".hero-product-card", { 
                duration: 0.8, opacity: 0, x: 100, stagger: 0.2, ease: "power2.out", delay: 0.5 
            });
        });
    </script>
</body>
</html>
