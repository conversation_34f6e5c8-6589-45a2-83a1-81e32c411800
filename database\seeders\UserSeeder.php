<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'phone' => '+233 24 123 4567',
            'address' => '123 Oxford Street, Osu, Accra, Ghana',
            'email_verified_at' => now(),
        ]);

        // Create sample customers
        $customers = [
            [
                'name' => 'Kofi Mensah',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'customer',
                'phone' => '+233 24 111 1111',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Ama Serwaa',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'customer',
                'phone' => '+233 24 222 2222',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Yaa <PERSON>a',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'customer',
                'phone' => '+233 24 333 3333',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Kwame Addo',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'customer',
                'phone' => '+233 24 444 4444',
                'email_verified_at' => now(),
            ],
        ];

        foreach ($customers as $customer) {
            User::create($customer);
        }
    }
}
