<?php

namespace Tests\Feature;

use App\Models\Product;
use App\Models\ProductOption;
use App\Models\ProductOptionValue;
use App\Services\ProductPricingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductPricingTest extends TestCase
{
    use RefreshDatabase;

    protected ProductPricingService $pricingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->pricingService = new ProductPricingService();
    }

    public function test_basic_product_pricing_calculation()
    {
        // Create a test product
        $product = Product::create([
            'name' => 'Test T-Shirt',
            'slug' => 'test-t-shirt',
            'description' => 'A test t-shirt',
            'category' => 'Apparel',
            'is_active' => true,
        ]);

        // Create size option
        $sizeOption = ProductOption::create([
            'product_id' => $product->id,
            'name' => 'Size',
            'type' => 'button',
            'sort_order' => 0,
        ]);

        $sizeS = ProductOptionValue::create([
            'product_option_id' => $sizeOption->id,
            'name' => 'S',
            'price' => 30.00,
            'sort_order' => 0,
        ]);

        $sizeL = ProductOptionValue::create([
            'product_option_id' => $sizeOption->id,
            'name' => 'L',
            'price' => 35.00,
            'sort_order' => 1,
        ]);

        // Create color option
        $colorOption = ProductOption::create([
            'product_id' => $product->id,
            'name' => 'Color',
            'type' => 'color',
            'sort_order' => 1,
        ]);

        $colorWhite = ProductOptionValue::create([
            'product_option_id' => $colorOption->id,
            'name' => 'White',
            'value' => '#FFFFFF',
            'price' => 0,
            'sort_order' => 0,
        ]);

        // Test pricing calculation
        $selectedOptions = [
            $sizeOption->id => $sizeS->id,
            $colorOption->id => $colorWhite->id,
        ];

        $pricing = $this->pricingService->calculatePrice($product, $selectedOptions);

        $this->assertEquals(30.00, $pricing['base_price']);
        $this->assertEquals(1, $pricing['multiplier']);
        $this->assertEquals(30.00, $pricing['final_price']);
        $this->assertEquals('₵30.00', $pricing['formatted_price']);

        // Test with larger size
        $selectedOptions = [
            $sizeOption->id => $sizeL->id,
            $colorOption->id => $colorWhite->id,
        ];

        $pricing = $this->pricingService->calculatePrice($product, $selectedOptions);

        $this->assertEquals(35.00, $pricing['base_price']);
        $this->assertEquals(35.00, $pricing['final_price']);
    }

    public function test_quantity_multiplier_pricing()
    {
        // Create a test product with quantity multipliers
        $product = Product::create([
            'name' => 'Test Business Cards',
            'slug' => 'test-business-cards',
            'description' => 'Test business cards',
            'category' => 'Office',
            'is_active' => true,
        ]);

        // Create type option
        $typeOption = ProductOption::create([
            'product_id' => $product->id,
            'name' => 'Type',
            'type' => 'button',
            'sort_order' => 0,
        ]);

        $typeMatte = ProductOptionValue::create([
            'product_option_id' => $typeOption->id,
            'name' => 'Matte',
            'price' => 150.00,
            'sort_order' => 0,
        ]);

        // Create quantity option
        $quantityOption = ProductOption::create([
            'product_id' => $product->id,
            'name' => 'Pack Size',
            'type' => 'quantity',
            'sort_order' => 1,
        ]);

        $quantity100 = ProductOptionValue::create([
            'product_option_id' => $quantityOption->id,
            'name' => 'x100',
            'multiplier' => 1,
            'sort_order' => 0,
        ]);

        $quantity200 = ProductOptionValue::create([
            'product_option_id' => $quantityOption->id,
            'name' => 'x200',
            'multiplier' => 1.8,
            'sort_order' => 1,
        ]);

        // Test with x100 quantity
        $selectedOptions = [
            $typeOption->id => $typeMatte->id,
            $quantityOption->id => $quantity100->id,
        ];

        $pricing = $this->pricingService->calculatePrice($product, $selectedOptions);

        $this->assertEquals(150.00, $pricing['base_price']);
        $this->assertEquals(1, $pricing['multiplier']);
        $this->assertEquals(150.00, $pricing['final_price']);

        // Test with x200 quantity (1.8x multiplier)
        $selectedOptions = [
            $typeOption->id => $typeMatte->id,
            $quantityOption->id => $quantity200->id,
        ];

        $pricing = $this->pricingService->calculatePrice($product, $selectedOptions);

        $this->assertEquals(150.00, $pricing['base_price']);
        $this->assertEquals(1.8, $pricing['multiplier']);
        $this->assertEquals(270.00, $pricing['final_price']); // 150 * 1.8
    }

    public function test_starting_price_calculation()
    {
        // Create a test product
        $product = Product::create([
            'name' => 'Test Product',
            'slug' => 'test-product',
            'description' => 'A test product',
            'category' => 'Test',
            'is_active' => true,
        ]);

        // Create option with different prices
        $option = ProductOption::create([
            'product_id' => $product->id,
            'name' => 'Size',
            'type' => 'button',
            'sort_order' => 0,
        ]);

        ProductOptionValue::create([
            'product_option_id' => $option->id,
            'name' => 'Small',
            'price' => 25.00,
            'sort_order' => 0,
        ]);

        ProductOptionValue::create([
            'product_option_id' => $option->id,
            'name' => 'Large',
            'price' => 40.00,
            'sort_order' => 1,
        ]);

        $startingPrice = $this->pricingService->getStartingPrice($product);

        // Should return the minimum price (25.00)
        $this->assertEquals(25.00, $startingPrice);
    }

    public function test_option_validation()
    {
        // Create a test product
        $product = Product::create([
            'name' => 'Test Product',
            'slug' => 'test-product',
            'description' => 'A test product',
            'category' => 'Test',
            'is_active' => true,
        ]);

        $option = ProductOption::create([
            'product_id' => $product->id,
            'name' => 'Size',
            'type' => 'button',
            'sort_order' => 0,
        ]);

        $value = ProductOptionValue::create([
            'product_option_id' => $option->id,
            'name' => 'Medium',
            'price' => 30.00,
            'sort_order' => 0,
        ]);

        // Test with missing options
        $errors = $this->pricingService->validateOptions($product, []);
        $this->assertCount(1, $errors);
        $this->assertStringContainsString('Please select a Size', $errors[0]);

        // Test with invalid option value
        $errors = $this->pricingService->validateOptions($product, [
            $option->id => 999 // Non-existent value ID
        ]);
        $this->assertCount(1, $errors);
        $this->assertStringContainsString('Invalid Size selection', $errors[0]);

        // Test with valid options
        $errors = $this->pricingService->validateOptions($product, [
            $option->id => $value->id
        ]);
        $this->assertCount(0, $errors);
    }
}
