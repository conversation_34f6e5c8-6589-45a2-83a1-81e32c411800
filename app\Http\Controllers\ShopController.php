<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;

class ShopController extends Controller
{
    /**
     * Display the shop page with all products
     */
    public function index(Request $request)
    {
        $query = Product::active()->with(['options.values']);

        // Filter by category if provided
        if ($request->has('category') && !empty($request->category)) {
            $query->category($request->category);
        }

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhere('category', 'like', '%' . $request->search . '%');
            });
        }

        // Sort options
        $sortBy = $request->get('sort', 'name');
        switch ($sortBy) {
            case 'price_low':
                // This is complex with our pricing structure, so we'll sort by name for now
                $query->orderBy('name');
                break;
            case 'price_high':
                $query->orderBy('name', 'desc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            default:
                $query->orderBy('sort_order')->orderBy('name');
        }

        $products = $query->paginate(12);

        // Get unique categories for filter
        $categories = Product::active()
            ->select('category')
            ->distinct()
            ->orderBy('category')
            ->pluck('category');

        return view('shop', compact('products', 'categories'));
    }
}
