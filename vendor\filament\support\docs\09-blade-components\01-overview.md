---
title: Overview
---

## Overview

Filament packages consume a set of core Blade components that aim to provide a consistent and maintainable foundation for all interfaces. Some of these components are also available for use in your own applications and Filament plugins.

## Available UI components

- [Avatar](avatar)
- [Badge](badge)
- [Breadcrumbs](breadcrumbs)
- [Loading indicator](loading-indicator)
- [Section](section)
- [Tabs](tabs)

### UI components for actions

- [Button](button)
- [Dropdown](dropdown)
- [Icon button](icon-button)
- [Link](link)
- [Modal](modal)

### UI components for forms

- [Checkbox](checkbox)
- [Fieldset](fieldset)
- [Input](input)
- [Input wrapper](input-wrapper)
- [Select](select)

### UI components for tables

- [Pagination](pagination)
