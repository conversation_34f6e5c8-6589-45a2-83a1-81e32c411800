<?php

namespace Database\Seeders;

use App\Models\Designer;
use Illuminate\Database\Seeder;

class DesignerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $designers = [
            [
                'name' => '<PERSON>a <PERSON>',
                'image_path' => 'https://placehold.co/128x128/E9D5FF/4C1D95?text=AS',
                'bio' => 'Specializing in vibrant branding and minimalist logo design.',
                'portfolio_url' => '#',
                'social_links' => [
                    'behance' => '#',
                    'instagram' => '#'
                ],
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => '<PERSON><PERSON>h',
                'image_path' => 'https://placehold.co/128x128/BBF7D0/14532D?text=KM',
                'bio' => 'Expert in corporate identity and print layout design.',
                'portfolio_url' => '#',
                'social_links' => [
                    'linkedin' => '#',
                    'dribbble' => '#'
                ],
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => '<PERSON><PERSON>',
                'image_path' => 'https://placehold.co/128x128/FECACA/7F1D1D?text=YA',
                'bio' => 'Creative illustrator and t-shirt design specialist.',
                'portfolio_url' => '#',
                'social_links' => [
                    'instagram' => '#',
                    'twitter' => '#'
                ],
                'is_active' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($designers as $designer) {
            Designer::create($designer);
        }
    }
}
