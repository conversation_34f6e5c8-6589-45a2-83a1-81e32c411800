<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? 'PrintOnline Ghana - On-Demand Printing & Delivery' }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Lato:wght@400;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Uppy CSS -->
    <link href="https://releases.transloadit.com/uppy/v3.2.1/uppy.min.css" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Livewire Styles -->
    @livewireStyles

    @stack('styles')
</head>
<body class="text-gray-800">
    <!-- Top Bar -->
    <div class="top-bar bg-white">
        <div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-2 text-xs text-gray-600">
            <span>Every time. Any reason. Or we'll make it right.</span>
            <span>Call for support: <strong>+233 24 123 4567</strong></span>
        </div>
    </div>

    <!-- Header -->
    <header class="sticky top-0 z-50 shadow-sm bg-white">
        <nav class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-4">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="{{ route('home') }}">
                    <img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" 
                         alt="PrintOnline Ghana Logo" class="h-10 mr-3">
                </a>
            </div>

            <!-- Navigation -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-pink-500 font-semibold {{ request()->routeIs('home') ? 'text-pink-500' : '' }}">HOME</a>
                <a href="{{ route('shop') }}" class="text-gray-600 hover:text-pink-500 font-semibold {{ request()->routeIs('shop') ? 'text-pink-500' : '' }}">SHOP</a>
                <a href="{{ route('designers') }}" class="text-gray-600 hover:text-pink-500 font-semibold {{ request()->routeIs('designers') ? 'text-pink-500' : '' }}">EXPERT DESIGNERS</a>
                <a href="{{ route('home') }}#how-it-works-section" class="text-gray-600 hover:text-pink-500 font-semibold">HOW IT WORKS</a>
            </div>

            <!-- Account Section -->
            <div class="flex items-center gap-6">
                @auth
                    <div class="flex items-center gap-2 bg-gray-100 py-2 px-4 rounded-full cursor-pointer" 
                         onclick="toggleAccountMenu()">
                        <i class="fas fa-user-circle text-gray-600 text-xl"></i>
                        <span class="text-sm font-semibold text-gray-700">{{ auth()->user()->first_name }}</span>
                        <i class="fas fa-chevron-down text-xs text-gray-500"></i>
                    </div>
                    
                    <!-- Account Dropdown -->
                    <div id="account-menu" class="hidden absolute top-16 right-4 bg-white shadow-lg rounded-lg py-2 w-48 z-50">
                        @if(auth()->user()->isAdmin())
                            <a href="{{ route('admin.dashboard') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-tachometer-alt mr-2"></i>Admin Dashboard
                            </a>
                        @endif
                        <a href="{{ route('orders.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-shopping-bag mr-2"></i>My Orders
                        </a>
                        <a href="{{ route('profile.edit') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-user mr-2"></i>Profile
                        </a>
                        <hr class="my-1">
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </button>
                        </form>
                    </div>
                @else
                    <div class="flex items-center gap-2 bg-gray-100 py-2 px-4 rounded-full cursor-pointer" 
                         onclick="showAuthModal()">
                        <i class="fas fa-user-circle text-gray-600 text-xl"></i>
                        <span class="text-sm font-semibold text-gray-700">Account</span>
                    </div>
                @endauth
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        {{ $slot }}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-screen-xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Logo and Description -->
                <div class="lg:col-span-1">
                    <div class="flex items-center">
                        <img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" 
                             alt="PrintOnline Ghana Logo" class="h-10 filter grayscale brightness-0 invert">
                    </div>
                    <p class="mt-4 text-gray-400 text-sm">
                        The best place to get your custom designs printed on high-quality products. Fast delivery across Ghana.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-sm font-semibold tracking-wider uppercase">Quick Links</h3>
                    <ul class="mt-4 space-y-2">
                        <li><a href="{{ route('home') }}" class="text-base text-gray-400 hover:text-white transition">Home</a></li>
                        <li><a href="{{ route('shop') }}?category=Apparel" class="text-base text-gray-400 hover:text-white transition">Apparel</a></li>
                        <li><a href="{{ route('shop') }}?category=Office" class="text-base text-gray-400 hover:text-white transition">Office</a></li>
                        <li><a href="{{ route('shop') }}?category=Giftware" class="text-base text-gray-400 hover:text-white transition">Gifts</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-sm font-semibold tracking-wider uppercase">Contact Us</h3>
                    <ul class="mt-4 space-y-3 text-gray-400 text-sm">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 mr-3 flex-shrink-0"></i>
                            <span>123 Oxford Street, Osu, Accra, Ghana</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone-alt mr-3"></i>
                            <span>+233 24 123 4567</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-3"></i>
                            <span><EMAIL></span>
                        </li>
                    </ul>
                </div>

                <!-- Social Media -->
                <div>
                    <h3 class="text-sm font-semibold tracking-wider uppercase">Follow Us</h3>
                    <div class="flex mt-4 space-x-6">
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-facebook-f fa-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-instagram fa-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-twitter fa-lg"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <i class="fab fa-linkedin-in fa-lg"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <div class="mt-12 border-t border-gray-800 pt-8 text-center text-sm text-gray-400">
                <p>&copy; {{ date('Y') }} PrintOnline Ghana. All rights reserved.</p>
            </div>
        </div>
    </footer>

    @guest
        <!-- Auth Modal -->
        @include('components.auth-modal')
    @endguest

    <!-- Livewire Scripts -->
    @livewireScripts

    <!-- External Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://releases.transloadit.com/uppy/v3.2.1/uppy.min.js"></script>

    @stack('scripts')

    <script>
        // Account menu toggle
        function toggleAccountMenu() {
            const menu = document.getElementById('account-menu');
            menu.classList.toggle('hidden');
        }

        // Close account menu when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('account-menu');
            const trigger = event.target.closest('[onclick="toggleAccountMenu()"]');
            
            if (!trigger && !menu.contains(event.target)) {
                menu.classList.add('hidden');
            }
        });

        // Auth modal functions (for guests)
        @guest
        function showAuthModal() {
            document.getElementById('auth-modal').classList.remove('hidden');
        }

        function hideAuthModal() {
            document.getElementById('auth-modal').classList.add('hidden');
        }
        @endguest
    </script>
</body>
</html>
