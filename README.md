# PrintOnline Ghana - Laravel Web Application

A full-stack Laravel web application for a print-on-demand service, built from HTML prototypes with complete backend functionality.

## 🚀 Features

### Frontend (Customer-Facing)
- **Responsive Design**: Mobile-first design using Tailwind CSS
- **Product Catalog**: Browse products with filtering and search
- **Dynamic Pricing**: Real-time price calculation based on product options
- **File Upload**: Design file upload with Uppy.js integration
- **Authentication**: Modal-based login/signup system
- **Order Management**: Complete order workflow
- **Expert Designers**: Showcase of design professionals
- **Reviews Carousel**: Animated customer testimonials

### Backend (Admin Dashboard)
- **Dashboard Analytics**: Revenue, orders, and user statistics
- **Order Management**: View and update order statuses
- **Product Management**: Full CRUD for products and variations
- **User Management**: Customer and admin user oversight
- **Settings Management**: Configurable site settings
- **File Management**: Integrated media library with Spatie MediaLibrary

### Technical Features
- **Product Pricing Engine**: Replicates JavaScript pricing logic in PHP
- **Role-Based Access**: Admin middleware and permissions
- **Database Relationships**: Comprehensive Eloquent models
- **File Uploads**: Secure file handling for design uploads
- **Responsive UI**: Matches original prototype designs
- **Testing**: Feature tests for critical functionality

## 🛠 Technology Stack

- **Backend**: Laravel 10.x
- **Frontend**: Blade Templates + Alpine.js + Tailwind CSS
- **Database**: MySQL/PostgreSQL
- **File Storage**: Spatie MediaLibrary
- **Charts**: Chart.js for admin analytics
- **File Upload**: Uppy.js integration
- **Authentication**: Laravel Breeze
- **Testing**: PHPUnit

## 📋 Installation & Setup

### Prerequisites
- PHP 8.1+
- Composer
- Node.js & NPM
- MySQL or PostgreSQL
- Web server (Apache/Nginx) or Laravel Valet/Sail

### Installation Steps

1. **Install Dependencies**
   ```bash
   composer install
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

3. **Database Setup**
   - Configure your database credentials in `.env`
   - Create the database:
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

4. **Storage Setup**
   ```bash
   php artisan storage:link
   ```

5. **Build Assets**
   ```bash
   npm run build
   # or for development
   npm run dev
   ```

6. **Start Development Server**
   ```bash
   php artisan serve
   ```

### Default Admin Account
- **Email**: <EMAIL>
- **Password**: password

### Sample Customer Accounts
- **Kofi Mensah**: <EMAIL> (password: password)
- **Ama Serwaa**: <EMAIL> (password: password)
- **Yaa Asantewaa**: <EMAIL> (password: password)

## 📊 Database Schema

### Core Tables
- `users` - Customer and admin accounts
- `products` - Product catalog
- `product_options` - Product variation types (Size, Color, etc.)
- `product_option_values` - Specific option values with pricing
- `orders` - Customer orders
- `order_items` - Individual order line items
- `designers` - Expert designer profiles
- `reviews` - Customer testimonials
- `settings` - Configurable site settings
- `media` - File attachments (Spatie MediaLibrary)

## 🎯 Key Features Implemented

### Product Pricing System
The application includes a sophisticated pricing engine that replicates the JavaScript logic from the prototype:

```php
// Example: Calculate price for a Custom T-Shirt
$selectedOptions = [
    'size_option_id' => 'large_value_id',    // +₵35.00
    'color_option_id' => 'blue_value_id',    // +₵0.00
];

$pricing = $pricingService->calculatePrice($product, $selectedOptions);
// Result: ₵35.00
```

### File Upload Integration
- Secure file upload for design files
- Support for images (JPG, PNG, GIF), PDFs, and SVG
- Automatic file validation and storage
- Integration with order items

### Admin Dashboard
- Real-time statistics and analytics
- Sales overview charts
- Order status management
- Product CRUD operations
- User management interface

## 🧪 Testing

Run the test suite:
```bash
php artisan test
```

Key test coverage:
- Product pricing calculations
- Option validation
- Order processing
- Authentication flows

## 🔧 Configuration

### Environment Variables
Key configuration options in `.env`:

```env
APP_NAME="PrintOnline Ghana"
APP_URL=http://localhost

DB_CONNECTION=mysql
DB_DATABASE=printonline_ghana
DB_USERNAME=root
DB_PASSWORD=

MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

### File Upload Limits
Configure in `config/filesystems.php` and server settings:
- Max file size: 10MB
- Allowed types: Images, PDF, SVG

## 📱 Pages & Routes

### Public Routes
- `/` - Home page with featured products
- `/shop` - Product catalog with filtering
- `/product/{slug}` - Product detail with pricing
- `/designers` - Expert designers showcase

### Protected Routes (Authenticated Users)
- `/orders` - User's order history
- `/orders/{order}` - Order details
- `/profile` - User profile management

### Admin Routes (Admin Users Only)
- `/admin/dashboard` - Analytics dashboard
- `/admin/orders` - Order management
- `/admin/products` - Product management
- `/admin/users` - User management
- `/admin/settings` - Site configuration

## 🎨 Design System

The application faithfully recreates the original HTML prototypes:

### Color Palette
- Primary: Pink (#ec4899)
- Secondary: Blue (#0d6efd)
- Success: Green (#10b981)
- Warning: Yellow (#f59e0b)
- Error: Red (#ef4444)

### Typography
- Headings: Poppins font family
- Body: Lato font family
- Consistent spacing and sizing

### Components
- Product cards with hover effects
- Modal authentication system
- Responsive navigation
- Animated reviews carousel
- Interactive pricing interface

## 🚀 Deployment Considerations

### Production Setup
1. Set `APP_ENV=production` in `.env`
2. Configure proper database credentials
3. Set up file storage (local or cloud)
4. Configure mail settings
5. Set up SSL certificates
6. Configure web server (Apache/Nginx)

### Performance Optimization
- Enable Laravel caching
- Optimize images and assets
- Configure CDN for static files
- Set up database indexing
- Enable gzip compression

## 📞 Support & Contact

For technical support or questions about this implementation:
- Review the code documentation
- Check the test suite for usage examples
- Refer to Laravel documentation for framework-specific questions

## 📄 License

This project is built for PrintOnline Ghana and includes proprietary business logic and design elements.

---

**Built with Laravel & Tailwind CSS** | **Faithful recreation of HTML prototypes with full backend functionality**
