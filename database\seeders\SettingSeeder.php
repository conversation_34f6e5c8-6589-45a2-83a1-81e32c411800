<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'PrintOnline Ghana',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Site Name',
                'description' => 'The name of the website',
            ],
            [
                'key' => 'site_tagline',
                'value' => 'On-Demand Printing & Delivery',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Site Tagline',
                'description' => 'The tagline or slogan for the website',
            ],
            [
                'key' => 'site_description',
                'value' => 'The best place to get your custom designs printed on high-quality products. Fast delivery across Ghana.',
                'type' => 'string',
                'group' => 'general',
                'label' => 'Site Description',
                'description' => 'A brief description of the website',
            ],

            // Contact Settings
            [
                'key' => 'contact_phone',
                'value' => '+233 24 123 4567',
                'type' => 'string',
                'group' => 'contact',
                'label' => 'Contact Phone',
                'description' => 'Main contact phone number',
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'contact',
                'label' => 'Contact Email',
                'description' => 'Main contact email address',
            ],
            [
                'key' => 'contact_address',
                'value' => '123 Oxford Street, Osu, Accra, Ghana',
                'type' => 'string',
                'group' => 'contact',
                'label' => 'Contact Address',
                'description' => 'Physical address of the business',
            ],

            // Social Media Settings
            [
                'key' => 'social_facebook',
                'value' => '#',
                'type' => 'string',
                'group' => 'social',
                'label' => 'Facebook URL',
                'description' => 'Facebook page URL',
            ],
            [
                'key' => 'social_instagram',
                'value' => '#',
                'type' => 'string',
                'group' => 'social',
                'label' => 'Instagram URL',
                'description' => 'Instagram profile URL',
            ],
            [
                'key' => 'social_twitter',
                'value' => '#',
                'type' => 'string',
                'group' => 'social',
                'label' => 'Twitter URL',
                'description' => 'Twitter profile URL',
            ],
            [
                'key' => 'social_linkedin',
                'value' => '#',
                'type' => 'string',
                'group' => 'social',
                'label' => 'LinkedIn URL',
                'description' => 'LinkedIn profile URL',
            ],

            // Business Settings
            [
                'key' => 'business_hours',
                'value' => 'Monday - Friday: 8:00 AM - 6:00 PM',
                'type' => 'string',
                'group' => 'business',
                'label' => 'Business Hours',
                'description' => 'Operating hours of the business',
            ],
            [
                'key' => 'delivery_areas',
                'value' => 'Greater Accra, Kumasi, Takoradi, Cape Coast',
                'type' => 'string',
                'group' => 'business',
                'label' => 'Delivery Areas',
                'description' => 'Areas where delivery is available',
            ],
            [
                'key' => 'minimum_order_amount',
                'value' => '50.00',
                'type' => 'float',
                'group' => 'business',
                'label' => 'Minimum Order Amount',
                'description' => 'Minimum order amount in GHS',
            ],

            // Email Settings
            [
                'key' => 'order_notification_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'email',
                'label' => 'Order Notification Email',
                'description' => 'Email address to receive order notifications',
            ],
            [
                'key' => 'design_team_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'email',
                'label' => 'Design Team Email',
                'description' => 'Email address for design inquiries',
            ],
        ];

        foreach ($settings as $setting) {
            Setting::create($setting);
        }
    }
}
