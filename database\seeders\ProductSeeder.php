<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\ProductOption;
use App\Models\ProductOptionValue;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Product data from the prototype
        $productsData = [
            [
                'name' => 'Custom T-Shirt',
                'category' => 'Apparel',
                'is_best_seller' => true,
                'is_featured' => true,
                'image' => 'https://placehold.co/400x400/F87171/FFFFFF?text=Shirt',
                'description' => 'Our best-selling custom t-shirt is made from 100% premium cotton. Perfect for personal designs, business logos, or special events. Available in multiple sizes and colors.',
                'options' => [
                    [
                        'name' => 'Size',
                        'type' => 'button',
                        'values' => [
                            ['name' => 'S', 'price' => 30.00],
                            ['name' => 'M', 'price' => 30.00],
                            ['name' => 'L', 'price' => 35.00],
                            ['name' => 'XL', 'price' => 38.00],
                        ]
                    ],
                    [
                        'name' => 'Color',
                        'type' => 'color',
                        'values' => [
                            ['name' => 'White', 'value' => '#FFFFFF', 'price' => 0],
                            ['name' => 'Black', 'value' => '#000000', 'price' => 0],
                            ['name' => 'Blue', 'value' => '#3B82F6', 'price' => 0],
                        ]
                    ]
                ]
            ],
            [
                'name' => 'Personalized Cup',
                'category' => 'Giftware',
                'is_best_seller' => false,
                'is_featured' => true,
                'image' => 'https://placehold.co/400x400/60A5FA/FFFFFF?text=Cup',
                'description' => 'Start your day with a personal touch. Our ceramic mugs are perfect for custom designs, logos, or personal messages.',
                'options' => [
                    [
                        'name' => 'Material',
                        'type' => 'button',
                        'values' => [
                            ['name' => 'Ceramic', 'price' => 20.00],
                            ['name' => 'Plastic', 'price' => 15.00],
                            ['name' => 'Bamboo', 'price' => 25.00],
                        ]
                    ]
                ]
            ],
            [
                'name' => 'Business Cards',
                'category' => 'Office',
                'is_best_seller' => true,
                'is_featured' => true,
                'image' => 'https://placehold.co/400x400/34D399/FFFFFF?text=Cards',
                'description' => 'Make a lasting impression with our high-quality business cards. Professional printing on premium paper stock.',
                'options' => [
                    [
                        'name' => 'Type',
                        'type' => 'button',
                        'values' => [
                            ['name' => 'Matte', 'price' => 150.00],
                            ['name' => 'Glossy', 'price' => 160.00],
                            ['name' => 'Untearable', 'price' => 200.00],
                        ]
                    ],
                    [
                        'name' => 'Pack Size',
                        'type' => 'quantity',
                        'values' => [
                            ['name' => 'x100', 'multiplier' => 1],
                            ['name' => 'x200', 'multiplier' => 1.8],
                            ['name' => 'x500', 'multiplier' => 4],
                        ]
                    ]
                ]
            ],
            [
                'name' => 'A5 Flyers',
                'category' => 'Marketing',
                'is_best_seller' => false,
                'is_featured' => true,
                'image' => 'https://placehold.co/400x400/FBBF24/FFFFFF?text=Flyer',
                'description' => 'Promote your event or business with eye-catching A5 flyers. High-quality printing on various paper weights.',
                'options' => [
                    [
                        'name' => 'Paper Weight',
                        'type' => 'button',
                        'values' => [
                            ['name' => '150gsm', 'price' => 200.00],
                            ['name' => '250gsm', 'price' => 250.00],
                            ['name' => '350gsm', 'price' => 300.00],
                        ]
                    ],
                    [
                        'name' => 'Pack Size',
                        'type' => 'quantity',
                        'values' => [
                            ['name' => 'x100', 'multiplier' => 1],
                            ['name' => 'x250', 'multiplier' => 2.2],
                            ['name' => 'x500', 'multiplier' => 4],
                        ]
                    ]
                ]
            ],
            [
                'name' => 'Custom Hoodie',
                'category' => 'Apparel',
                'is_best_seller' => false,
                'is_featured' => false,
                'image' => 'https://placehold.co/400x400/9CA3AF/FFFFFF?text=Hoodie',
                'description' => 'Stay warm and stylish with our custom hoodies. Perfect for teams, events, or personal wear.',
                'options' => [
                    [
                        'name' => 'Size',
                        'type' => 'button',
                        'values' => [
                            ['name' => 'M', 'price' => 80.00],
                            ['name' => 'L', 'price' => 85.00],
                            ['name' => 'XL', 'price' => 90.00],
                        ]
                    ],
                    [
                        'name' => 'Color',
                        'type' => 'color',
                        'values' => [
                            ['name' => 'Grey', 'value' => '#6B7280', 'price' => 0],
                            ['name' => 'Black', 'value' => '#1F2937', 'price' => 0],
                        ]
                    ]
                ]
            ],
            [
                'name' => 'Tote Bag',
                'category' => 'Accessories',
                'is_best_seller' => true,
                'is_featured' => false,
                'image' => 'https://placehold.co/400x400/FCD34D/FFFFFF?text=Tote',
                'description' => 'A durable and eco-friendly tote bag perfect for shopping, events, or daily use.',
                'options' => [
                    [
                        'name' => 'Material',
                        'type' => 'button',
                        'values' => [
                            ['name' => 'Canvas', 'price' => 25.00],
                            ['name' => 'Cotton', 'price' => 22.00],
                        ]
                    ]
                ]
            ],
            [
                'name' => 'A3 Poster',
                'category' => 'Marketing',
                'is_best_seller' => false,
                'is_featured' => false,
                'image' => 'https://placehold.co/400x400/A78BFA/FFFFFF?text=Poster',
                'description' => 'High-quality A3 posters perfect for advertising, events, or decoration.',
                'options' => [
                    [
                        'name' => 'Finish',
                        'type' => 'button',
                        'values' => [
                            ['name' => 'Matte', 'price' => 50.00],
                            ['name' => 'Gloss', 'price' => 55.00],
                        ]
                    ]
                ]
            ],
            [
                'name' => 'Roll-up Banner',
                'category' => 'Marketing',
                'is_best_seller' => false,
                'is_featured' => false,
                'image' => 'https://placehold.co/400x400/38BDF8/FFFFFF?text=Banner',
                'description' => 'Make a big impact with our professional roll-up banners. Perfect for trade shows and events.',
                'options' => [
                    [
                        'name' => 'Size',
                        'type' => 'button',
                        'values' => [
                            ['name' => 'Standard', 'price' => 450.00],
                            ['name' => 'Large', 'price' => 600.00],
                        ]
                    ]
                ]
            ],
            [
                'name' => 'Sticker Sheet (A4)',
                'category' => 'Office',
                'is_best_seller' => true,
                'is_featured' => false,
                'image' => 'https://placehold.co/400x400/FB923C/FFFFFF?text=Stickers',
                'description' => 'Create custom die-cut stickers for branding, decoration, or fun. High-quality vinyl material.',
                'options' => [
                    [
                        'name' => 'Finish',
                        'type' => 'button',
                        'values' => [
                            ['name' => 'Matte', 'price' => 40.00],
                            ['name' => 'Glossy', 'price' => 45.00],
                        ]
                    ]
                ]
            ],
            [
                'name' => 'Custom Jumper',
                'category' => 'Apparel',
                'is_best_seller' => false,
                'is_featured' => false,
                'image' => 'https://placehold.co/400x400/4B5563/FFFFFF?text=Jumper',
                'description' => 'A comfortable and warm jumper perfect for cooler weather. Customize with your design.',
                'options' => [
                    [
                        'name' => 'Size',
                        'type' => 'button',
                        'values' => [
                            ['name' => 'M', 'price' => 70.00],
                            ['name' => 'L', 'price' => 75.00],
                            ['name' => 'XL', 'price' => 80.00],
                        ]
                    ],
                    [
                        'name' => 'Color',
                        'type' => 'color',
                        'values' => [
                            ['name' => 'Navy', 'value' => '#1E3A8A', 'price' => 0],
                            ['name' => 'Charcoal', 'value' => '#374151', 'price' => 0],
                        ]
                    ]
                ]
            ]
        ];

        foreach ($productsData as $index => $productData) {
            $product = Product::create([
                'name' => $productData['name'],
                'slug' => Str::slug($productData['name']),
                'description' => $productData['description'],
                'image_path' => $productData['image'],
                'category' => $productData['category'],
                'is_best_seller' => $productData['is_best_seller'],
                'is_featured' => $productData['is_featured'],
                'is_active' => true,
                'sort_order' => $index,
            ]);

            foreach ($productData['options'] as $optionIndex => $optionData) {
                $option = ProductOption::create([
                    'product_id' => $product->id,
                    'name' => $optionData['name'],
                    'type' => $optionData['type'],
                    'sort_order' => $optionIndex,
                ]);

                foreach ($optionData['values'] as $valueIndex => $valueData) {
                    ProductOptionValue::create([
                        'product_option_id' => $option->id,
                        'name' => $valueData['name'],
                        'value' => $valueData['value'] ?? null,
                        'price' => isset($valueData['price']) ? $valueData['price'] : 0,
                        'multiplier' => isset($valueData['multiplier']) ? $valueData['multiplier'] : 1,
                        'sort_order' => $valueIndex,
                    ]);
                }
            }
        }
    }
}
