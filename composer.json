{"name": "printonline-ghana/printonline-ghana", "type": "project", "description": "PrintOnline Ghana - On-Demand Printing & Delivery Platform", "keywords": ["laravel", "framework", "printing", "e-commerce"], "license": "MIT", "require": {"php": "^8.1", "consoletvs/charts": "^6.0", "filament/filament": "^3.2", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "10.48", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.8", "livewire/livewire": "^3.0", "spatie/laravel-medialibrary": "^10.0"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/breeze": "^1.29", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^7.10", "phpunit/phpunit": "^10.5", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}