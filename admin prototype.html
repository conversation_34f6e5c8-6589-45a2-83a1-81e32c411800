<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - PrintOnline Ghana</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Chart.js for data visuals -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Lato:wght@400;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Lato', sans-serif;
            background-color: #f1f5f9;
        }
        h1, h2, h3, h4, h5, h6, .font-heading {
            font-family: 'Poppins', sans-serif;
        }
        .sidebar-link {
            transition: background-color 0.2s, color 0.2s;
        }
        .sidebar-link.active, .sidebar-link:hover {
            background-color: #fb7185;
            color: white;
        }
        .admin-page {
            display: none;
        }
        .admin-page.active {
            display: block;
        }
    </style>
</head>
<body class="flex h-screen">

    <!-- Sidebar -->
    <aside class="w-64 bg-slate-800 text-white flex flex-col">
        <div class="flex items-center justify-center p-6 border-b border-slate-700">
            <img src="https://printonlineghana.com/wp-content/uploads/2020/10/cropped-printonlinelogo-tr-1-2.png" alt="Logo" class="h-10 filter grayscale brightness-0 invert">
        </div>
        <nav class="flex-1 px-4 py-6 space-y-2">
            <a href="#" class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-lg active" data-page="dashboard">
                <i class="fas fa-tachometer-alt w-6 text-center"></i>
                <span class="font-semibold">Dashboard</span>
            </a>
            <a href="#" class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-lg" data-page="orders">
                <i class="fas fa-shopping-cart w-6 text-center"></i>
                <span class="font-semibold">Orders</span>
            </a>
            <a href="#" class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-lg" data-page="products">
                <i class="fas fa-box w-6 text-center"></i>
                <span class="font-semibold">Products</span>
            </a>
            <a href="#" class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-lg" data-page="users">
                <i class="fas fa-users w-6 text-center"></i>
                <span class="font-semibold">Users</span>
            </a>
            <a href="#" class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-lg" data-page="settings">
                <i class="fas fa-cog w-6 text-center"></i>
                <span class="font-semibold">Settings</span>
            </a>
        </nav>
        <div class="p-6 border-t border-slate-700">
            <a href="#" class="flex items-center gap-3 text-slate-400 hover:text-white">
                <i class="fas fa-sign-out-alt w-6 text-center"></i>
                <span class="font-semibold">Logout</span>
            </a>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 p-6 lg:p-10 overflow-y-auto">
        
        <!-- Dashboard Page -->
        <div id="dashboard-page" class="admin-page active">
            <h1 class="text-3xl font-bold text-slate-800 mb-8">Dashboard</h1>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <div class="bg-white p-6 rounded-xl shadow-md"><h3 class="text-slate-500 font-semibold">Total Revenue</h3><p class="text-3xl font-bold mt-2">₵12,450</p></div>
                <div class="bg-white p-6 rounded-xl shadow-md"><h3 class="text-slate-500 font-semibold">New Orders</h3><p class="text-3xl font-bold mt-2">82</p></div>
                <div class="bg-white p-6 rounded-xl shadow-md"><h3 class="text-slate-500 font-semibold">New Users</h3><p class="text-3xl font-bold mt-2">15</p></div>
            </div>
            <div class="bg-white p-6 rounded-xl shadow-md">
                <h3 class="text-lg font-bold mb-4">Sales Overview (Last 7 Days)</h3>
                <canvas id="salesChart"></canvas>
            </div>
        </div>

        <!-- Orders Page -->
        <div id="orders-page" class="admin-page">
            <h1 class="text-3xl font-bold text-slate-800 mb-8">Orders Management</h1>
            <div class="bg-white p-6 rounded-xl shadow-md overflow-x-auto">
                <table class="w-full text-left">
                    <thead><tr class="border-b"><th class="p-4">Order ID</th><th class="p-4">Customer</th><th class="p-4">Product</th><th class="p-4">Amount</th><th class="p-4">Status</th></tr></thead>
                    <tbody>
                        <tr class="border-b"><td class="p-4">#1024</td><td class="p-4">Kofi Mensah</td><td class="p-4">Custom T-Shirt</td><td class="p-4">₵35.00</td><td class="p-4"><span class="bg-yellow-200 text-yellow-800 text-xs font-semibold px-2 py-1 rounded-full">Pending</span></td></tr>
                        <tr class="border-b"><td class="p-4">#1023</td><td class="p-4">Ama Serwaa</td><td class="p-4">Business Cards</td><td class="p-4">₵150.00</td><td class="p-4"><span class="bg-blue-200 text-blue-800 text-xs font-semibold px-2 py-1 rounded-full">Printing</span></td></tr>
                        <tr class="border-b"><td class="p-4">#1022</td><td class="p-4">Yaa Asantewaa</td><td class="p-4">A3 Poster</td><td class="p-4">₵50.00</td><td class="p-4"><span class="bg-green-200 text-green-800 text-xs font-semibold px-2 py-1 rounded-full">Completed</span></td></tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Products Page -->
        <div id="products-page" class="admin-page">
            <h1 class="text-3xl font-bold text-slate-800 mb-8">Products Management</h1>
            <div class="bg-white p-6 rounded-xl shadow-md overflow-x-auto">
                <table class="w-full text-left">
                    <thead><tr class="border-b"><th class="p-4">Product Name</th><th class="p-4">Category</th><th class="p-4">Base Price</th><th class="p-4">Actions</th></tr></thead>
                    <tbody>
                        <tr class="border-b"><td class="p-4">Custom T-Shirt</td><td class="p-4">Apparel</td><td class="p-4">₵30.00</td><td class="p-4 text-sky-500"><a href="#" class="hover:underline">Edit</a></td></tr>
                        <tr class="border-b"><td class="p-4">Personalized Cup</td><td class="p-4">Giftware</td><td class="p-4">₵15.00</td><td class="p-4 text-sky-500"><a href="#" class="hover:underline">Edit</a></td></tr>
                        <tr class="border-b"><td class="p-4">Business Cards</td><td class="p-4">Office</td><td class="p-4">₵150.00</td><td class="p-4 text-sky-500"><a href="#" class="hover:underline">Edit</a></td></tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Users Page -->
        <div id="users-page" class="admin-page">
            <h1 class="text-3xl font-bold text-slate-800 mb-8">Users Management</h1>
            <div class="bg-white p-6 rounded-xl shadow-md overflow-x-auto">
                <table class="w-full text-left">
                    <thead><tr class="border-b"><th class="p-4">User ID</th><th class="p-4">Name</th><th class="p-4">Email</th><th class="p-4">Joined Date</th></tr></thead>
                    <tbody>
                        <tr class="border-b"><td class="p-4">#001</td><td class="p-4">Kofi Mensah</td><td class="p-4"><EMAIL></td><td class="p-4">2024-08-01</td></tr>
                        <tr class="border-b"><td class="p-4">#002</td><td class="p-4">Ama Serwaa</td><td class="p-4"><EMAIL></td><td class="p-4">2024-07-28</td></tr>
                        <tr class="border-b"><td class="p-4">#003</td><td class="p-4">Yaa Asantewaa</td><td class="p-4"><EMAIL></td><td class="p-4">2024-07-25</td></tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Settings Page -->
        <div id="settings-page" class="admin-page">
            <h1 class="text-3xl font-bold text-slate-800 mb-8">Settings</h1>
            <div class="bg-white p-6 rounded-xl shadow-md">
                <form class="space-y-6">
                    <div><label class="font-semibold">Store Name</label><input type="text" value="PrintOnline Ghana" class="w-full mt-2 p-3 border rounded-lg"></div>
                    <div><label class="font-semibold">Support Email</label><input type="email" value="<EMAIL>" class="w-full mt-2 p-3 border rounded-lg"></div>
                    <div><label class="font-semibold">Support Phone</label><input type="tel" value="+233 24 123 4567" class="w-full mt-2 p-3 border rounded-lg"></div>
                    <button type="submit" class="bg-pink-500 text-white font-bold py-3 px-6 rounded-lg hover:bg-pink-600 transition">Save Changes</button>
                </form>
            </div>
        </div>

    </main>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const sidebarLinks = document.querySelectorAll('.sidebar-link');
            const adminPages = document.querySelectorAll('.admin-page');

            sidebarLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    sidebarLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');
                    
                    const pageId = link.dataset.page + '-page';
                    adminPages.forEach(page => {
                        if (page.id === pageId) {
                            page.classList.add('active');
                        } else {
                            page.classList.remove('active');
                        }
                    });
                });
            });

            // Chart.js implementation
            const ctx = document.getElementById('salesChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [{
                        label: 'Sales (₵)',
                        data: [1200, 1900, 1500, 2500, 2200, 3000, 2800],
                        backgroundColor: 'rgba(251, 113, 133, 0.1)',
                        borderColor: '#fb7185',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true,
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
