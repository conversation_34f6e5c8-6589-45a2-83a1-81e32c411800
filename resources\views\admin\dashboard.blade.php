<x-layouts.admin title="Dashboard">
    <x-slot name="pageTitle">Dashboard</x-slot>
    <x-slot name="pageDescription">Overview of your print shop performance</x-slot>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Revenue -->
        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-slate-500 font-semibold text-sm">Total Revenue</h3>
                    <p class="text-3xl font-bold mt-2">₵{{ number_format($stats['total_revenue'], 2) }}</p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- New Orders -->
        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-slate-500 font-semibold text-sm">New Orders (30 days)</h3>
                    <p class="text-3xl font-bold mt-2">{{ $stats['new_orders'] }}</p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-shopping-cart text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- New Users -->
        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-slate-500 font-semibold text-sm">New Users (30 days)</h3>
                    <p class="text-3xl font-bold mt-2">{{ $stats['new_users'] }}</p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-purple-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Products -->
        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-slate-500 font-semibold text-sm">Active Products</h3>
                    <p class="text-3xl font-bold mt-2">{{ $stats['total_products'] }}</p>
                </div>
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-box text-orange-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Sales Chart -->
        <div class="bg-white p-6 rounded-xl shadow-md">
            <h3 class="text-lg font-bold mb-4">Sales Overview (Last 7 Days)</h3>
            <canvas id="salesChart" width="400" height="200"></canvas>
        </div>

        <!-- Recent Orders -->
        <div class="bg-white p-6 rounded-xl shadow-md">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold">Recent Orders</h3>
                <a href="{{ route('admin.orders.index') }}" 
                   class="text-pink-500 hover:text-pink-600 text-sm font-semibold">
                    View All
                </a>
            </div>
            
            @if($recentOrders->count() > 0)
                <div class="space-y-4">
                    @foreach($recentOrders->take(5) as $order)
                        <div class="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                            <div>
                                <p class="font-semibold text-sm">{{ $order->order_number }}</p>
                                <p class="text-xs text-gray-500">{{ $order->user->name }}</p>
                                <p class="text-xs text-gray-400">{{ $order->created_at->format('M j, Y g:i A') }}</p>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-sm">{{ $order->formatted_total }}</p>
                                <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full
                                    @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                                    @elseif($order->status === 'confirmed') bg-blue-100 text-blue-800
                                    @elseif($order->status === 'printing') bg-purple-100 text-purple-800
                                    @elseif($order->status === 'completed') bg-green-100 text-green-800
                                    @else bg-red-100 text-red-800
                                    @endif">
                                    {{ ucfirst($order->status) }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-shopping-cart text-3xl mb-2"></i>
                    <p>No orders yet</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <a href="{{ route('admin.orders.index') }}" 
           class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-shopping-cart text-blue-600 text-xl"></i>
                </div>
                <div>
                    <h4 class="font-semibold">Manage Orders</h4>
                    <p class="text-sm text-gray-500">View and update order status</p>
                </div>
            </div>
        </a>

        <a href="{{ route('admin.products.index') }}" 
           class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-box text-green-600 text-xl"></i>
                </div>
                <div>
                    <h4 class="font-semibold">Manage Products</h4>
                    <p class="text-sm text-gray-500">Add and edit products</p>
                </div>
            </div>
        </a>

        <a href="{{ route('admin.settings.index') }}" 
           class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow">
            <div class="flex items-center gap-4">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-cog text-purple-600 text-xl"></i>
                </div>
                <div>
                    <h4 class="font-semibold">Settings</h4>
                    <p class="text-sm text-gray-500">Configure site settings</p>
                </div>
            </div>
        </a>
    </div>

    @push('scripts')
    <script>
        // Sales Chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: @json($chartLabels),
                datasets: [{
                    label: 'Sales (₵)',
                    data: @json($chartData),
                    backgroundColor: 'rgba(251, 113, 133, 0.1)',
                    borderColor: '#fb7185',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₵' + value;
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
    @endpush
</x-layouts.admin>
