<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Services\ProductPricingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ProductController extends Controller
{
    protected ProductPricingService $pricingService;

    public function __construct(ProductPricingService $pricingService)
    {
        $this->pricingService = $pricingService;
    }

    /**
     * Display the specified product
     */
    public function show(Product $product)
    {
        // Load product with all related data
        $product->load(['options.values']);

        // Get default pricing
        $defaultPricing = $this->pricingService->getDefaultPrice($product);

        return view('product.show', compact('product', 'defaultPricing'));
    }

    /**
     * Calculate price for selected options via API
     */
    public function calculatePrice(Request $request, Product $product): JsonResponse
    {
        $request->validate([
            'options' => 'required|array',
            'options.*' => 'required|integer|exists:product_option_values,id',
        ]);

        try {
            $pricing = $this->pricingService->calculatePrice($product, $request->options);
            
            return response()->json([
                'success' => true,
                'pricing' => $pricing,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error calculating price: ' . $e->getMessage(),
            ], 400);
        }
    }
}
