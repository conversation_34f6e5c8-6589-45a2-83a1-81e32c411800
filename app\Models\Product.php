<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Product extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'image_path',
        'category',
        'is_best_seller',
        'is_featured',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_best_seller' => 'boolean',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the product's options
     */
    public function options()
    {
        return $this->hasMany(ProductOption::class)->orderBy('sort_order');
    }

    /**
     * Get the product's order items
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Scope a query to only include active products.
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('is_active', true);
    }

    /**
     * Scope a query to only include featured products.
     */
    public function scopeFeatured(Builder $query): void
    {
        $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include best seller products.
     */
    public function scopeBestSeller(Builder $query): void
    {
        $query->where('is_best_seller', true);
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeCategory(Builder $query, string $category): void
    {
        $query->where('category', $category);
    }

    /**
     * Get the starting price for this product
     */
    public function getStartingPriceAttribute(): float
    {
        $firstOption = $this->options()->first();
        if (!$firstOption) {
            return 0;
        }

        $minPrice = $firstOption->values()->min('price');
        return $minPrice ?? 0;
    }

    /**
     * Get the product image URL
     */
    public function getImageUrlAttribute(): string
    {
        if ($this->image_path) {
            return asset('storage/' . $this->image_path);
        }
        
        // Return placeholder if no image
        return "https://placehold.co/400x400/F87171/FFFFFF?text=" . urlencode($this->name);
    }

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('images')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
    }

    /**
     * Register media conversions
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(400)
            ->height(400)
            ->sharpen(10);

        $this->addMediaConversion('large')
            ->width(800)
            ->height(800)
            ->sharpen(10);
    }
}
