<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Designer extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'name',
        'image_path',
        'bio',
        'portfolio_url',
        'social_links',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'social_links' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Scope a query to only include active designers.
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('is_active', true);
    }

    /**
     * Get the designer's image URL
     */
    public function getImageUrlAttribute(): string
    {
        if ($this->image_path) {
            return asset('storage/' . $this->image_path);
        }
        
        // Generate placeholder with initials
        $initials = $this->getInitialsAttribute();
        return "https://placehold.co/128x128/E9D5FF/4C1D95?text=" . urlencode($initials);
    }

    /**
     * Get the designer's initials
     */
    public function getInitialsAttribute(): string
    {
        $words = explode(' ', $this->name);
        $initials = '';
        
        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= strtoupper($word[0]);
            }
        }
        
        return substr($initials, 0, 2);
    }

    /**
     * Get social media links as formatted HTML
     */
    public function getSocialLinksHtmlAttribute(): string
    {
        if (empty($this->social_links)) {
            return '';
        }

        $html = '';
        $iconMap = [
            'behance' => 'fab fa-behance',
            'instagram' => 'fab fa-instagram',
            'linkedin' => 'fab fa-linkedin-in',
            'dribbble' => 'fab fa-dribbble',
            'twitter' => 'fab fa-twitter',
            'facebook' => 'fab fa-facebook-f',
        ];

        foreach ($this->social_links as $platform => $url) {
            if (!empty($url) && isset($iconMap[$platform])) {
                $html .= sprintf(
                    '<a href="%s" target="_blank" class="text-gray-400 hover:text-pink-500 transition"><i class="%s fa-lg"></i></a>',
                    htmlspecialchars($url),
                    $iconMap[$platform]
                );
            }
        }

        return $html;
    }

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('avatar')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
    }

    /**
     * Register media conversions
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(128)
            ->height(128)
            ->sharpen(10);
    }
}
