<x-layouts.app title="PrintOnline Ghana - On-Demand Printing & Delivery">
    <!-- Hero Section -->
    <section class="relative bg-white pt-16 pb-24 sm:pt-24 sm:pb-32">
        <div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 grid md:grid-cols-2 gap-12 items-center">
            <!-- Left Column: Text Content -->
            <div class="text-center md:text-left">
                <h1 class="font-heading text-4xl sm:text-5xl lg:text-6xl font-extrabold tracking-tight text-gray-900">
                    PRINT WHATEVER YOU WANT
                </h1>
                <p class="mt-6 text-lg text-gray-600">
                    From t-shirt design to stationery we print everything. We do everything and everything!
                </p>
                <a href="{{ route('shop') }}" 
                   class="font-heading mt-8 inline-block bg-pink-500 text-white font-bold py-3 px-8 rounded-lg text-lg hover:bg-pink-600 transition duration-300 transform hover:scale-105">
                    Send For Printing <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>

            <!-- Right Column: Hero Product Cards -->
            <div class="relative h-96">
                <div class="hero-product-card absolute top-0 left-10 w-48" style="transform: rotate(-10deg);">
                    <img src="https://printy.themerex.net/wp-content/uploads/2023/11/img-12-copyright.png" 
                         alt="T-Shirt" class="w-full h-auto">
                    <p class="text-center font-semibold text-sm p-2 bg-white">T-SHIRTS AND APPAREL</p>
                </div>
                <div class="hero-product-card absolute top-1/4 left-1/3 w-40 z-10" style="transform: rotate(5deg);">
                    <img src="https://printy.themerex.net/wp-content/uploads/2023/11/product-11-copyright-630x630.jpg" 
                         alt="Bottle" class="w-full h-auto">
                    <p class="text-center font-semibold text-sm p-2 bg-white">PRODUCT AND LABEL</p>
                </div>
                <div class="hero-product-card absolute top-10 right-10 w-40" style="transform: rotate(15deg);">
                    <img src="https://printy.themerex.net/wp-content/uploads/2023/11/product-12-copyright-630x630.jpg" 
                         alt="Phone Case" class="w-full h-auto">
                    <p class="text-center font-semibold text-sm p-2 bg-white">PHONE BACK COVERS</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section id="products-section" class="py-24 bg-gray-50">
        <div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center mb-16 text-gray-900">Featured Products</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                @foreach($featuredProducts as $product)
                    <x-product-card :product="$product" />
                @endforeach
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works-section" class="relative py-24 bg-blue-600">
        <div class="relative max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 z-10">
            <h2 class="text-3xl font-bold text-center mb-16 text-white">HOW IT WORKS</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8 text-white">
                <!-- Step 1 -->
                <div class="how-it-works-step flex flex-col items-center text-center">
                    <div class="step-number w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg text-white bg-blue-500 mb-4 border-3 border-white">
                        01
                    </div>
                    <h3 class="font-semibold mb-2">Choose Product</h3>
                    <p class="text-sm opacity-80">Choose the product you want from our catalog.</p>
                </div>

                <!-- Step 2 -->
                <div class="how-it-works-step flex flex-col items-center text-center">
                    <div class="step-number w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg text-white bg-blue-500 mb-4 border-3 border-white">
                        02
                    </div>
                    <h3 class="font-semibold mb-2">Upload Design</h3>
                    <p class="text-sm opacity-80">Apply your pre-uploaded designs or create a new one.</p>
                </div>

                <!-- Step 3 -->
                <div class="how-it-works-step flex flex-col items-center text-center">
                    <div class="step-number w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg text-white bg-blue-500 mb-4 border-3 border-white">
                        03
                    </div>
                    <h3 class="font-semibold mb-2">Place Order</h3>
                    <p class="text-sm opacity-80">Complete your design, make payment and place order.</p>
                </div>

                <!-- Step 4 -->
                <div class="how-it-works-step flex flex-col items-center text-center">
                    <div class="step-number w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg text-white bg-blue-500 mb-4 border-3 border-white">
                        04
                    </div>
                    <h3 class="font-semibold mb-2">Get Delivery</h3>
                    <p class="text-sm opacity-80">Get your product delivered at your doorstep.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Google Reviews Section -->
    <section id="reviews-section" class="py-24 bg-gray-50">
        <div class="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center mb-4 text-gray-900">What Our Customers Say</h2>
            <p class="text-center text-gray-600 mb-8">Rated 4.9/5.0 on Google Reviews</p>
            
            <div class="reviews-slider">
                <div class="reviews-track">
                    @foreach($reviews as $review)
                        <div class="review-card">
                            <div class="flex items-start justify-between mb-4">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/c/c1/Google_%22G%22_logo.svg" 
                                     alt="Google" class="h-6">
                                <div class="text-yellow-400">
                                    {!! $review->star_rating_html !!}
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 text-sm leading-relaxed">"{{ $review->review_text }}"</p>
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm mr-3"
                                     style="background-color: {{ $review->avatar_color }}; color: {{ $review->avatar_text_color }}">
                                    {{ $review->avatar_initials }}
                                </div>
                                <span class="font-semibold text-gray-800">{{ $review->customer_name }}</span>
                            </div>
                        </div>
                    @endforeach
                    
                    <!-- Duplicate reviews for seamless loop -->
                    @foreach($reviews as $review)
                        <div class="review-card">
                            <div class="flex items-start justify-between mb-4">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/c/c1/Google_%22G%22_logo.svg" 
                                     alt="Google" class="h-6">
                                <div class="text-yellow-400">
                                    {!! $review->star_rating_html !!}
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4 text-sm leading-relaxed">"{{ $review->review_text }}"</p>
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm mr-3"
                                     style="background-color: {{ $review->avatar_color }}; color: {{ $review->avatar_text_color }}">
                                    {{ $review->avatar_initials }}
                                </div>
                                <span class="font-semibold text-gray-800">{{ $review->customer_name }}</span>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section id="cta-section" class="py-20 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                Have a design? Let's bring it to life.
            </h2>
            <p class="mt-4 text-lg text-gray-600">
                Our high-quality printing services are ready for your creativity. Upload your design and get started today.
            </p>
            <a href="{{ route('shop') }}" 
               class="mt-8 inline-block bg-pink-500 text-white font-bold py-4 px-10 rounded-lg text-lg hover:bg-pink-600 transition duration-300 font-heading">
                Upload & Print
            </a>
        </div>
    </section>
</x-layouts.app>
